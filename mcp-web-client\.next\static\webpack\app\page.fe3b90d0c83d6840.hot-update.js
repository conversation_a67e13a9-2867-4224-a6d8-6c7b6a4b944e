"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/ConnectionList.tsx":
/*!*******************************************!*\
  !*** ./src/components/ConnectionList.tsx ***!
  \*******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ConnectionList: () => (/* binding */ ConnectionList)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _MCPClientProvider__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./MCPClientProvider */ \"(app-pages-browser)/./src/components/MCPClientProvider.tsx\");\n/* __next_internal_client_entry_do_not_use__ ConnectionList auto */ \nvar _s = $RefreshSig$();\n\n\nfunction ConnectionList() {\n    _s();\n    const { connections, activeConnection, connectToServer, disconnectFromServer, removeConnection, setActiveConnection } = (0,_MCPClientProvider__WEBPACK_IMPORTED_MODULE_2__.useMCPClientContext)();\n    const [loadingConnections, setLoadingConnections] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Set());\n    const handleConnect = async (connectionId)=>{\n        setLoadingConnections((prev)=>new Set(prev).add(connectionId));\n        try {\n            await connectToServer(connectionId);\n        } catch (error) {\n            console.error('Error connecting:', error);\n            // Mostrar mensaje más específico según el tipo de error\n            const errorMessage = error instanceof Error ? error.message : 'Error desconocido';\n            if (errorMessage.includes('CORS') || errorMessage.includes('SSE connection')) {\n                alert(\"Error de conexi\\xf3n: \".concat(errorMessage, \"\\n\\nPara conexiones SSE, aseg\\xfarate de que:\\n1. El servidor est\\xe9 ejecut\\xe1ndose en la URL especificada\\n2. El servidor tenga CORS configurado\\n3. El servidor soporte Server-Sent Events\"));\n            }\n        } finally{\n            setLoadingConnections((prev)=>{\n                const newSet = new Set(prev);\n                newSet.delete(connectionId);\n                return newSet;\n            });\n        }\n    };\n    const handleDisconnect = async (connectionId)=>{\n        setLoadingConnections((prev)=>new Set(prev).add(connectionId));\n        try {\n            await disconnectFromServer(connectionId);\n        } catch (error) {\n            console.error('Error disconnecting:', error);\n        } finally{\n            setLoadingConnections((prev)=>{\n                const newSet = new Set(prev);\n                newSet.delete(connectionId);\n                return newSet;\n            });\n        }\n    };\n    const handleRemove = async (connectionId)=>{\n        if (confirm('¿Estás seguro de que quieres eliminar esta conexión?')) {\n            try {\n                await removeConnection(connectionId);\n            } catch (error) {\n                console.error('Error removing connection:', error);\n            }\n        }\n    };\n    const getStatusColor = (connection)=>{\n        if (connection.status.connecting) return 'text-yellow-600 bg-yellow-100 dark:bg-yellow-900 dark:text-yellow-300';\n        if (connection.status.connected) return 'text-green-600 bg-green-100 dark:bg-green-900 dark:text-green-300';\n        if (connection.status.error) return 'text-red-600 bg-red-100 dark:bg-red-900 dark:text-red-300';\n        return 'text-gray-600 bg-gray-100 dark:bg-gray-700 dark:text-gray-300';\n    };\n    const getStatusText = (connection)=>{\n        if (connection.status.connecting) return 'Conectando...';\n        if (connection.status.connected) return 'Conectado';\n        if (connection.status.error) return 'Error';\n        return 'Desconectado';\n    };\n    const getTransportIcon = (type)=>{\n        switch(type){\n            case 'stdio':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                    className: \"w-4 h-4\",\n                    fill: \"none\",\n                    stroke: \"currentColor\",\n                    viewBox: \"0 0 24 24\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        strokeLinecap: \"round\",\n                        strokeLinejoin: \"round\",\n                        strokeWidth: 2,\n                        d: \"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\serverN8N\\\\MCPClient\\\\mcp-web-client\\\\src\\\\components\\\\ConnectionList.tsx\",\n                        lineNumber: 84,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\serverN8N\\\\MCPClient\\\\mcp-web-client\\\\src\\\\components\\\\ConnectionList.tsx\",\n                    lineNumber: 83,\n                    columnNumber: 11\n                }, this);\n            case 'sse':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                    className: \"w-4 h-4\",\n                    fill: \"none\",\n                    stroke: \"currentColor\",\n                    viewBox: \"0 0 24 24\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        strokeLinecap: \"round\",\n                        strokeLinejoin: \"round\",\n                        strokeWidth: 2,\n                        d: \"M13 10V3L4 14h7v7l9-11h-7z\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\serverN8N\\\\MCPClient\\\\mcp-web-client\\\\src\\\\components\\\\ConnectionList.tsx\",\n                        lineNumber: 90,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\serverN8N\\\\MCPClient\\\\mcp-web-client\\\\src\\\\components\\\\ConnectionList.tsx\",\n                    lineNumber: 89,\n                    columnNumber: 11\n                }, this);\n            case 'http':\n            case 'streamable-http':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                    className: \"w-4 h-4\",\n                    fill: \"none\",\n                    stroke: \"currentColor\",\n                    viewBox: \"0 0 24 24\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        strokeLinecap: \"round\",\n                        strokeLinejoin: \"round\",\n                        strokeWidth: 2,\n                        d: \"M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9a9 9 0 01-9-9m9 9c1.657 0 3-4.03 3-9s-1.343-9-3-9m0 18c-1.657 0-3-4.03-3-9s1.343-9 3-9m-9 9a9 9 0 019-9\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\serverN8N\\\\MCPClient\\\\mcp-web-client\\\\src\\\\components\\\\ConnectionList.tsx\",\n                        lineNumber: 97,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\serverN8N\\\\MCPClient\\\\mcp-web-client\\\\src\\\\components\\\\ConnectionList.tsx\",\n                    lineNumber: 96,\n                    columnNumber: 11\n                }, this);\n            default:\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                    className: \"w-4 h-4\",\n                    fill: \"none\",\n                    stroke: \"currentColor\",\n                    viewBox: \"0 0 24 24\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        strokeLinecap: \"round\",\n                        strokeLinejoin: \"round\",\n                        strokeWidth: 2,\n                        d: \"M8.111 16.404a5.5 5.5 0 017.778 0M12 20h.01m-7.08-7.071c3.904-3.905 10.236-3.905 14.141 0M1.394 9.393c5.857-5.857 15.355-5.857 21.213 0\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\serverN8N\\\\MCPClient\\\\mcp-web-client\\\\src\\\\components\\\\ConnectionList.tsx\",\n                        lineNumber: 103,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\serverN8N\\\\MCPClient\\\\mcp-web-client\\\\src\\\\components\\\\ConnectionList.tsx\",\n                    lineNumber: 102,\n                    columnNumber: 11\n                }, this);\n        }\n    };\n    if (connections.length === 0) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"text-center py-12\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                    className: \"mx-auto h-12 w-12 text-gray-400\",\n                    fill: \"none\",\n                    stroke: \"currentColor\",\n                    viewBox: \"0 0 24 24\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        strokeLinecap: \"round\",\n                        strokeLinejoin: \"round\",\n                        strokeWidth: 2,\n                        d: \"M8.111 16.404a5.5 5.5 0 017.778 0M12 20h.01m-7.08-7.071c3.904-3.905 10.236-3.905 14.141 0M1.394 9.393c5.857-5.857 15.355-5.857 21.213 0\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\serverN8N\\\\MCPClient\\\\mcp-web-client\\\\src\\\\components\\\\ConnectionList.tsx\",\n                        lineNumber: 113,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"D:\\\\serverN8N\\\\MCPClient\\\\mcp-web-client\\\\src\\\\components\\\\ConnectionList.tsx\",\n                    lineNumber: 112,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                    className: \"mt-2 text-sm font-medium text-gray-900 dark:text-white\",\n                    children: \"No hay conexiones\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\serverN8N\\\\MCPClient\\\\mcp-web-client\\\\src\\\\components\\\\ConnectionList.tsx\",\n                    lineNumber: 115,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"mt-1 text-sm text-gray-500 dark:text-gray-400\",\n                    children: \"Crea una nueva conexi\\xf3n para comenzar a usar MCP.\"\n                }, void 0, false, {\n                    fileName: \"D:\\\\serverN8N\\\\MCPClient\\\\mcp-web-client\\\\src\\\\components\\\\ConnectionList.tsx\",\n                    lineNumber: 118,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"D:\\\\serverN8N\\\\MCPClient\\\\mcp-web-client\\\\src\\\\components\\\\ConnectionList.tsx\",\n            lineNumber: 111,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-4\",\n        children: connections.map((connection)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"border rounded-lg p-4 transition-all \".concat(activeConnection === connection.id ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20' : 'border-gray-200 dark:border-gray-700 hover:border-gray-300 dark:hover:border-gray-600'),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            getTransportIcon(connection.config.type),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-medium text-gray-900 dark:text-white\",\n                                                children: connection.name\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\serverN8N\\\\MCPClient\\\\mcp-web-client\\\\src\\\\components\\\\ConnectionList.tsx\",\n                                                lineNumber: 140,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\serverN8N\\\\MCPClient\\\\mcp-web-client\\\\src\\\\components\\\\ConnectionList.tsx\",\n                                        lineNumber: 138,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium \".concat(getStatusColor(connection)),\n                                        children: getStatusText(connection)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\serverN8N\\\\MCPClient\\\\mcp-web-client\\\\src\\\\components\\\\ConnectionList.tsx\",\n                                        lineNumber: 144,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\serverN8N\\\\MCPClient\\\\mcp-web-client\\\\src\\\\components\\\\ConnectionList.tsx\",\n                                lineNumber: 137,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2\",\n                                children: [\n                                    !connection.status.connected && !connection.status.connecting && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>handleConnect(connection.id),\n                                        disabled: loadingConnections.has(connection.id),\n                                        className: \"bg-green-600 hover:bg-green-700 disabled:bg-green-400 text-white px-3 py-1 rounded text-sm font-medium transition-colors\",\n                                        children: loadingConnections.has(connection.id) ? 'Conectando...' : 'Conectar'\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\serverN8N\\\\MCPClient\\\\mcp-web-client\\\\src\\\\components\\\\ConnectionList.tsx\",\n                                        lineNumber: 151,\n                                        columnNumber: 17\n                                    }, this),\n                                    connection.status.connected && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>setActiveConnection(connection.id),\n                                                className: \"px-3 py-1 rounded text-sm font-medium transition-colors \".concat(activeConnection === connection.id ? 'bg-blue-600 text-white' : 'bg-gray-200 hover:bg-gray-300 text-gray-700 dark:bg-gray-600 dark:hover:bg-gray-500 dark:text-gray-200'),\n                                                children: activeConnection === connection.id ? 'Activa' : 'Activar'\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\serverN8N\\\\MCPClient\\\\mcp-web-client\\\\src\\\\components\\\\ConnectionList.tsx\",\n                                                lineNumber: 162,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: ()=>handleDisconnect(connection.id),\n                                                disabled: loadingConnections.has(connection.id),\n                                                className: \"bg-red-600 hover:bg-red-700 disabled:bg-red-400 text-white px-3 py-1 rounded text-sm font-medium transition-colors\",\n                                                children: loadingConnections.has(connection.id) ? 'Desconectando...' : 'Desconectar'\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\serverN8N\\\\MCPClient\\\\mcp-web-client\\\\src\\\\components\\\\ConnectionList.tsx\",\n                                                lineNumber: 172,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>handleRemove(connection.id),\n                                        className: \"text-gray-400 hover:text-red-600 dark:hover:text-red-400 p-1\",\n                                        title: \"Eliminar conexi\\xf3n\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"w-4 h-4\",\n                                            fill: \"none\",\n                                            stroke: \"currentColor\",\n                                            viewBox: \"0 0 24 24\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\",\n                                                strokeWidth: 2,\n                                                d: \"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\serverN8N\\\\MCPClient\\\\mcp-web-client\\\\src\\\\components\\\\ConnectionList.tsx\",\n                                                lineNumber: 188,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\serverN8N\\\\MCPClient\\\\mcp-web-client\\\\src\\\\components\\\\ConnectionList.tsx\",\n                                            lineNumber: 187,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\serverN8N\\\\MCPClient\\\\mcp-web-client\\\\src\\\\components\\\\ConnectionList.tsx\",\n                                        lineNumber: 182,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\serverN8N\\\\MCPClient\\\\mcp-web-client\\\\src\\\\components\\\\ConnectionList.tsx\",\n                                lineNumber: 149,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\serverN8N\\\\MCPClient\\\\mcp-web-client\\\\src\\\\components\\\\ConnectionList.tsx\",\n                        lineNumber: 136,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-3 text-sm text-gray-600 dark:text-gray-300\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 md:grid-cols-2 gap-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-medium\",\n                                                children: \"Tipo:\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\serverN8N\\\\MCPClient\\\\mcp-web-client\\\\src\\\\components\\\\ConnectionList.tsx\",\n                                                lineNumber: 197,\n                                                columnNumber: 17\n                                            }, this),\n                                            \" \",\n                                            connection.config.type.toUpperCase()\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\serverN8N\\\\MCPClient\\\\mcp-web-client\\\\src\\\\components\\\\ConnectionList.tsx\",\n                                        lineNumber: 196,\n                                        columnNumber: 15\n                                    }, this),\n                                    connection.config.url && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-medium\",\n                                                children: \"URL:\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\serverN8N\\\\MCPClient\\\\mcp-web-client\\\\src\\\\components\\\\ConnectionList.tsx\",\n                                                lineNumber: 201,\n                                                columnNumber: 19\n                                            }, this),\n                                            \" \",\n                                            connection.config.url\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\serverN8N\\\\MCPClient\\\\mcp-web-client\\\\src\\\\components\\\\ConnectionList.tsx\",\n                                        lineNumber: 200,\n                                        columnNumber: 17\n                                    }, this),\n                                    connection.config.command && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-medium\",\n                                                children: \"Comando:\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\serverN8N\\\\MCPClient\\\\mcp-web-client\\\\src\\\\components\\\\ConnectionList.tsx\",\n                                                lineNumber: 206,\n                                                columnNumber: 19\n                                            }, this),\n                                            \" \",\n                                            connection.config.command,\n                                            connection.config.args && connection.config.args.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"ml-1\",\n                                                children: connection.config.args.join(' ')\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\serverN8N\\\\MCPClient\\\\mcp-web-client\\\\src\\\\components\\\\ConnectionList.tsx\",\n                                                lineNumber: 208,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\serverN8N\\\\MCPClient\\\\mcp-web-client\\\\src\\\\components\\\\ConnectionList.tsx\",\n                                        lineNumber: 205,\n                                        columnNumber: 17\n                                    }, this),\n                                    connection.status.serverInfo && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-medium\",\n                                                children: \"Servidor:\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\serverN8N\\\\MCPClient\\\\mcp-web-client\\\\src\\\\components\\\\ConnectionList.tsx\",\n                                                lineNumber: 214,\n                                                columnNumber: 19\n                                            }, this),\n                                            \" \",\n                                            connection.status.serverInfo.name,\n                                            \" v\",\n                                            connection.status.serverInfo.version\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\serverN8N\\\\MCPClient\\\\mcp-web-client\\\\src\\\\components\\\\ConnectionList.tsx\",\n                                        lineNumber: 213,\n                                        columnNumber: 17\n                                    }, this),\n                                    connection.status.lastConnected && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-medium\",\n                                                children: \"\\xdaltima conexi\\xf3n:\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\serverN8N\\\\MCPClient\\\\mcp-web-client\\\\src\\\\components\\\\ConnectionList.tsx\",\n                                                lineNumber: 219,\n                                                columnNumber: 19\n                                            }, this),\n                                            \" \",\n                                            connection.status.lastConnected.toLocaleString()\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\serverN8N\\\\MCPClient\\\\mcp-web-client\\\\src\\\\components\\\\ConnectionList.tsx\",\n                                        lineNumber: 218,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\serverN8N\\\\MCPClient\\\\mcp-web-client\\\\src\\\\components\\\\ConnectionList.tsx\",\n                                lineNumber: 195,\n                                columnNumber: 13\n                            }, this),\n                            connection.status.error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-2 p-2 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded text-red-700 dark:text-red-300 text-xs\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-medium\",\n                                        children: \"Error:\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\serverN8N\\\\MCPClient\\\\mcp-web-client\\\\src\\\\components\\\\ConnectionList.tsx\",\n                                        lineNumber: 226,\n                                        columnNumber: 17\n                                    }, this),\n                                    \" \",\n                                    connection.status.error\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\serverN8N\\\\MCPClient\\\\mcp-web-client\\\\src\\\\components\\\\ConnectionList.tsx\",\n                                lineNumber: 225,\n                                columnNumber: 15\n                            }, this),\n                            connection.status.connected && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-2 flex space-x-4 text-xs\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-medium\",\n                                                children: \"Herramientas:\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\serverN8N\\\\MCPClient\\\\mcp-web-client\\\\src\\\\components\\\\ConnectionList.tsx\",\n                                                lineNumber: 233,\n                                                columnNumber: 19\n                                            }, this),\n                                            \" \",\n                                            connection.tools.length\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\serverN8N\\\\MCPClient\\\\mcp-web-client\\\\src\\\\components\\\\ConnectionList.tsx\",\n                                        lineNumber: 232,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-medium\",\n                                                children: \"Recursos:\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\serverN8N\\\\MCPClient\\\\mcp-web-client\\\\src\\\\components\\\\ConnectionList.tsx\",\n                                                lineNumber: 236,\n                                                columnNumber: 19\n                                            }, this),\n                                            \" \",\n                                            connection.resources.length\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\serverN8N\\\\MCPClient\\\\mcp-web-client\\\\src\\\\components\\\\ConnectionList.tsx\",\n                                        lineNumber: 235,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"font-medium\",\n                                                children: \"Prompts:\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\serverN8N\\\\MCPClient\\\\mcp-web-client\\\\src\\\\components\\\\ConnectionList.tsx\",\n                                                lineNumber: 239,\n                                                columnNumber: 19\n                                            }, this),\n                                            \" \",\n                                            connection.prompts.length\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\serverN8N\\\\MCPClient\\\\mcp-web-client\\\\src\\\\components\\\\ConnectionList.tsx\",\n                                        lineNumber: 238,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\serverN8N\\\\MCPClient\\\\mcp-web-client\\\\src\\\\components\\\\ConnectionList.tsx\",\n                                lineNumber: 231,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\serverN8N\\\\MCPClient\\\\mcp-web-client\\\\src\\\\components\\\\ConnectionList.tsx\",\n                        lineNumber: 194,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, connection.id, true, {\n                fileName: \"D:\\\\serverN8N\\\\MCPClient\\\\mcp-web-client\\\\src\\\\components\\\\ConnectionList.tsx\",\n                lineNumber: 128,\n                columnNumber: 9\n            }, this))\n    }, void 0, false, {\n        fileName: \"D:\\\\serverN8N\\\\MCPClient\\\\mcp-web-client\\\\src\\\\components\\\\ConnectionList.tsx\",\n        lineNumber: 126,\n        columnNumber: 5\n    }, this);\n}\n_s(ConnectionList, \"mJoLURhdhPqf7FaAZTwfXOiqhP8=\", false, function() {\n    return [\n        _MCPClientProvider__WEBPACK_IMPORTED_MODULE_2__.useMCPClientContext\n    ];\n});\n_c = ConnectionList;\nvar _c;\n$RefreshReg$(_c, \"ConnectionList\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ConnectionList.tsx\n"));

/***/ })

});