(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[974],{5800:(e,t,r)=>{"use strict";let s;r.r(t),r.d(t,{default:()=>L});var n=r(5155),a=r(2115),o=r(9087);let i={randomUUID:"undefined"!=typeof crypto&&crypto.randomUUID&&crypto.randomUUID.bind(crypto)},c=new Uint8Array(16),l=[];for(let e=0;e<256;++e)l.push((e+256).toString(16).slice(1));let d=function(e,t,r){if(i.randomUUID&&!t&&!e)return i.randomUUID();let n=(e=e||{}).random??e.rng?.()??function(){if(!s){if("undefined"==typeof crypto||!crypto.getRandomValues)throw Error("crypto.getRandomValues() not supported. See https://github.com/uuidjs/uuid#getrandomvalues-not-supported");s=crypto.getRandomValues.bind(crypto)}return s(c)}();if(n.length<16)throw Error("Random bytes length must be >= 16");if(n[6]=15&n[6]|64,n[8]=63&n[8]|128,t){if((r=r||0)<0||r+16>t.length)throw RangeError(`UUID byte range ${r}:${r+15} is out of buffer bounds`);for(let e=0;e<16;++e)t[r+e]=n[e];return t}return function(e,t=0){return(l[e[t+0]]+l[e[t+1]]+l[e[t+2]]+l[e[t+3]]+"-"+l[e[t+4]]+l[e[t+5]]+"-"+l[e[t+6]]+l[e[t+7]]+"-"+l[e[t+8]]+l[e[t+9]]+"-"+l[e[t+10]]+l[e[t+11]]+l[e[t+12]]+l[e[t+13]]+l[e[t+14]]+l[e[t+15]]).toLowerCase()}(n)};class h extends o.EventEmitter{isConnected(){return this.connected}isConnecting(){return this.connecting}handleMessage(e){this.emit("message",e)}handleError(e){this.emit("error",e)}handleConnect(){this.connected=!0,this.connecting=!1,this.emit("connect")}handleDisconnect(){this.connected=!1,this.connecting=!1,this.emit("disconnect")}constructor(...e){super(...e),this.connected=!1,this.connecting=!1}}class m extends o.EventEmitter{async connect(e){this.transport&&await this.disconnect(),this.transport=e,this.setupTransportListeners();try{await this.transport.connect(),await this.initialize()}catch(e){throw this.transport=null,e}}async disconnect(){this.transport&&(this.removeTransportListeners(),await this.transport.disconnect(),this.transport=null),this.initialized=!1,this.serverInfo=null,this.tools=[],this.resources=[],this.prompts=[],this.clearPendingRequests()}setupTransportListeners(){this.transport&&(this.transport.on("message",this.handleMessage.bind(this)),this.transport.on("error",this.handleTransportError.bind(this)),this.transport.on("disconnect",this.handleTransportDisconnect.bind(this)))}removeTransportListeners(){this.transport&&(this.transport.removeAllListeners("message"),this.transport.removeAllListeners("error"),this.transport.removeAllListeners("disconnect"))}handleMessage(e){if(void 0!==e.id){let t=this.pendingRequests.get(e.id);t&&(clearTimeout(t.timeout),this.pendingRequests.delete(e.id),e.error?t.reject(Error("MCP Error ".concat(e.error.code,": ").concat(e.error.message))):t.resolve(e.result))}else e.method&&this.handleNotification(e);this.emit("message",e)}handleNotification(e){switch(e.method){case"notifications/message":this.emit("log",e.params);break;case"notifications/tools/list_changed":this.refreshTools();break;case"notifications/resources/list_changed":this.refreshResources();break;case"notifications/prompts/list_changed":this.refreshPrompts()}}handleTransportError(e){this.emit("error",e)}handleTransportDisconnect(){this.initialized=!1,this.emit("disconnect")}async sendRequest(e,t){if(!this.transport||!this.transport.isConnected())throw Error("No hay conexi\xf3n activa");let r=d(),s={jsonrpc:"2.0",id:r,method:e,params:t};return new Promise((t,n)=>{let a=setTimeout(()=>{this.pendingRequests.delete(r),n(Error("Timeout: No se recibi\xf3 respuesta para ".concat(e)))},this.requestTimeout);this.pendingRequests.set(r,{resolve:t,reject:n,timeout:a}),this.transport.send(s).catch(n)})}async initialize(){let e={protocolVersion:"2024-11-05",capabilities:this.capabilities,clientInfo:this.clientInfo},t=await this.sendRequest("initialize",e);this.serverInfo=t.serverInfo,await this.sendNotification("notifications/initialized"),await Promise.all([this.refreshTools(),this.refreshResources(),this.refreshPrompts()]),this.initialized=!0,this.emit("initialized",t)}async sendNotification(e,t){if(!this.transport||!this.transport.isConnected())throw Error("No hay conexi\xf3n activa");await this.transport.send({jsonrpc:"2.0",method:e,params:t})}async refreshTools(){try{let e=await this.sendRequest("tools/list");this.tools=e.tools||[],this.emit("toolsChanged",this.tools)}catch(e){console.error("Error refreshing tools:",e)}}async refreshResources(){try{let e=await this.sendRequest("resources/list");this.resources=e.resources||[],this.emit("resourcesChanged",this.resources)}catch(e){console.error("Error refreshing resources:",e)}}async refreshPrompts(){try{let e=await this.sendRequest("prompts/list");this.prompts=e.prompts||[],this.emit("promptsChanged",this.prompts)}catch(e){console.error("Error refreshing prompts:",e)}}clearPendingRequests(){for(let[e,t]of this.pendingRequests)clearTimeout(t.timeout),t.reject(Error("Conexi\xf3n cerrada"));this.pendingRequests.clear()}isInitialized(){return this.initialized}isConnected(){var e;return(null==(e=this.transport)?void 0:e.isConnected())||!1}getServerInfo(){return this.serverInfo}getTools(){return[...this.tools]}getResources(){return[...this.resources]}getPrompts(){return[...this.prompts]}async callTool(e){if(!this.initialized)throw Error("Cliente no inicializado");let t=await this.sendRequest("tools/call",e);return this.emit("toolResult",e.name,t),t}async readResource(e){if(!this.initialized)throw Error("Cliente no inicializado");return await this.sendRequest("resources/read",e)}async getPrompt(e){if(!this.initialized)throw Error("Cliente no inicializado");return await this.sendRequest("prompts/get",e)}async setLoggingLevel(e){if(!this.initialized)throw Error("Cliente no inicializado");await this.sendNotification("notifications/logging/setLevel",{level:e})}constructor(e,t={}){super(),this.transport=null,this.pendingRequests=new Map,this.requestTimeout=3e4,this.serverInfo=null,this.tools=[],this.resources=[],this.prompts=[],this.initialized=!1,this.clientInfo=e,this.capabilities=t}}var u=r(9509);class x extends h{async connect(){if(!this.connecting&&!this.connected){this.connecting=!0;try{await this.connectViaBrowserAPI()}catch(e){throw this.connecting=!1,e}}}async connectViaBrowserAPI(){try{let e=new WebSocket("ws://localhost:3001/mcp-proxy");e.onopen=()=>{e.send(JSON.stringify({type:"start_process",command:this.config.command,args:this.config.args||[],env:this.config.env||{}}))},e.onmessage=e=>{let t=JSON.parse(e.data);"process_started"===t.type?this.handleConnect():"stdout"===t.type?this.handleStdout(t.data):"stderr"===t.type?console.warn("MCP Server stderr:",t.data):"error"===t.type?this.handleError(Error(t.message)):"exit"===t.type&&this.handleDisconnect()},e.onerror=e=>{this.handleError(Error("WebSocket error: "+e))},e.onclose=()=>{this.handleDisconnect()},this.process=e,await new Promise((e,t)=>{let r=setTimeout(()=>{t(Error("Timeout connecting to MCP proxy"))},1e4),s=()=>{clearTimeout(r),this.removeListener("error",n),e()},n=e=>{clearTimeout(r),this.removeListener("connect",s),t(e)};this.once("connect",s),this.once("error",n)})}catch(e){throw Error("Failed to connect via browser API: ".concat(e))}}async connectViaNodeJS(){try{var e;if(void 0===u||!(null==(e=u.versions)?void 0:e.node))throw Error("Node.js environment required for STDIO transport");let{spawn:t}=await r.e(428).then(r.t.bind(r,5428,23));this.process=t(this.config.command,this.config.args||[],{env:{...u.env,...this.config.env},stdio:["pipe","pipe","pipe"]}),this.process.stdout.on("data",e=>{this.handleStdout(e.toString())}),this.process.stderr.on("data",e=>{console.warn("MCP Server stderr:",e.toString())}),this.process.on("error",e=>{this.handleError(e)}),this.process.on("exit",e=>{0!==e?this.handleError(Error("Process exited with code ".concat(e))):this.handleDisconnect()}),await new Promise(e=>setTimeout(e,100)),this.handleConnect()}catch(e){throw Error("Failed to spawn process: ".concat(e))}}handleStdout(e){this.messageBuffer+=e;let t=this.messageBuffer.split("\n");for(let e of(this.messageBuffer=t.pop()||"",t))if(e.trim())try{let t=JSON.parse(e.trim());this.handleMessage(t)}catch(t){console.error("Error parsing MCP message:",t,"Line:",e)}}async send(e){if(!this.connected||!this.process)throw Error("Not connected");let t=JSON.stringify(e)+"\n";this.process.send(JSON.stringify({type:"stdin",data:t}))}async disconnect(){this.process&&(this.process.readyState===WebSocket.OPEN&&(this.process.send(JSON.stringify({type:"stop_process"})),this.process.close()),this.process=null,this.messageBuffer="",this.handleDisconnect())}constructor(e){super(),this.process=null,this.messageBuffer="",this.config=e}}class g extends h{async connect(){if(!this.connecting&&!this.connected){this.connecting=!0;try{await this.establishSSEConnection()}catch(e){throw this.connecting=!1,e}}}async establishSSEConnection(){return new Promise((e,t)=>{let r=new URL(this.config.url);this.lastEventId&&r.searchParams.set("Last-Event-ID",this.lastEventId),this.eventSource=new EventSource(r.toString());let s=setTimeout(()=>{this.cleanup(),t(Error("Timeout connecting to SSE endpoint"))},1e4);this.eventSource.onopen=()=>{clearTimeout(s),console.log("SSE connection opened")},this.eventSource.onmessage=e=>{try{let t=JSON.parse(e.data);e.lastEventId&&(this.lastEventId=e.lastEventId),this.handleMessage(t)}catch(e){console.error("Error parsing SSE message:",e)}},this.eventSource.addEventListener("endpoint",r=>{try{let t=JSON.parse(r.data);t.uri&&(this.messageEndpoint=t.uri),clearTimeout(s),this.handleConnect(),e()}catch(e){clearTimeout(s),t(Error("Invalid endpoint event data"))}}),this.eventSource.addEventListener("message",e=>{try{let t=JSON.parse(e.data);e.lastEventId&&(this.lastEventId=e.lastEventId),this.handleMessage(t)}catch(e){console.error("Error parsing MCP message from SSE:",e)}}),this.eventSource.onerror=e=>{var t;clearTimeout(s),(null==(t=this.eventSource)?void 0:t.readyState)===EventSource.CLOSED?this.handleDisconnect():this.handleReconnect()}})}handleReconnect(){this.connected&&(console.log("SSE connection lost, attempting to reconnect..."),setTimeout(async()=>{try{await this.establishSSEConnection(),console.log("SSE reconnection successful")}catch(e){console.error("SSE reconnection failed:",e),this.handleError(Error("Failed to reconnect to SSE endpoint"))}},1e3))}async send(e){if(!this.connected)throw Error("Not connected to SSE endpoint");try{let t=await fetch(this.messageEndpoint,{method:"POST",headers:{"Content-Type":"application/json",...this.sessionId&&{"Mcp-Session-Id":this.sessionId}},body:JSON.stringify(e)});if(!t.ok)throw Error("HTTP ".concat(t.status,": ").concat(t.statusText));let r=t.headers.get("Mcp-Session-Id");r&&!this.sessionId&&(this.sessionId=r);let s=t.headers.get("Content-Type");if(null==s?void 0:s.includes("application/json")){let e=await t.json();e&&this.handleMessage(e)}}catch(e){throw Error("Failed to send message: ".concat(e))}}async disconnect(){this.cleanup(),this.handleDisconnect()}cleanup(){this.eventSource&&(this.eventSource.close(),this.eventSource=null),this.sessionId=null,this.lastEventId=null}constructor(e){super(),this.eventSource=null,this.sessionId=null,this.lastEventId=null,this.config=e,this.messageEndpoint=e.messageEndpoint||this.config.url.replace("/sse","/messages")}}class p extends h{async connect(){if(!this.connecting&&!this.connected){this.connecting=!0;try{await this.detectServerCapabilities(),this.handleConnect()}catch(e){throw this.connecting=!1,e}}}async detectServerCapabilities(){try{var e;if((await fetch(this.config.url,{method:"POST",headers:{"Content-Type":"application/json",Accept:"application/json, text/event-stream","MCP-Protocol-Version":this.protocolVersion,...this.config.headers},body:JSON.stringify({jsonrpc:"2.0",method:"ping",id:"capability-test"})})).ok)return void console.log("Server supports Streamable HTTP transport");let t=await fetch(this.config.url,{method:"GET",headers:{Accept:"text/event-stream",...this.config.headers}});if(t.ok&&(null==(e=t.headers.get("Content-Type"))?void 0:e.includes("text/event-stream"))){this.supportsSSE=!0,console.log("Server supports legacy SSE transport"),await this.setupSSEStream();return}throw Error("Server does not support any known MCP transport")}catch(e){throw Error("Failed to detect server capabilities: ".concat(e))}}async setupSSEStream(){return new Promise((e,t)=>{this.sseStream=new EventSource(this.config.url);let r=setTimeout(()=>{this.cleanup(),t(Error("Timeout establishing SSE stream"))},1e4);this.sseStream.onopen=()=>{console.log("SSE stream established")},this.sseStream.addEventListener("endpoint",s=>{try{let t=JSON.parse(s.data);console.log("Received endpoint event:",t),clearTimeout(r),e()}catch(e){clearTimeout(r),t(Error("Invalid endpoint event"))}}),this.sseStream.addEventListener("message",e=>{try{let t=JSON.parse(e.data);this.handleMessage(t)}catch(e){console.error("Error parsing SSE message:",e)}}),this.sseStream.onerror=e=>{var s;clearTimeout(r),(null==(s=this.sseStream)?void 0:s.readyState)===EventSource.CLOSED?this.handleDisconnect():t(Error("SSE stream error"))}})}async send(e){if(!this.connected)throw Error("Not connected");try{let t=await fetch(this.config.url,{method:"POST",headers:{"Content-Type":"application/json",Accept:"application/json, text/event-stream","MCP-Protocol-Version":this.protocolVersion,...this.sessionId&&{"Mcp-Session-Id":this.sessionId},...this.config.headers},body:JSON.stringify(e)});if(!t.ok){if(404===t.status&&this.sessionId)throw this.sessionId=null,Error("Session expired, please reconnect");throw Error("HTTP ".concat(t.status,": ").concat(t.statusText))}let r=t.headers.get("Mcp-Session-Id");r&&!this.sessionId&&(this.sessionId=r);let s=t.headers.get("Content-Type");if(null==s?void 0:s.includes("text/event-stream"))await this.handleStreamingResponse(t);else if(null==s?void 0:s.includes("application/json")){let e=await t.json();e&&this.handleMessage(e)}else if(202===t.status)return}catch(e){throw Error("Failed to send message: ".concat(e))}}async handleStreamingResponse(e){if(!e.body)throw Error("No response body for streaming response");let t=e.body.getReader(),r=new TextDecoder,s="";try{for(;;){let{done:e,value:n}=await t.read();if(e)break;s+=r.decode(n,{stream:!0});let a=this.parseSSEEvents(s);for(let e of(s=a.remainder,a.events))if(e.data)try{let t=JSON.parse(e.data);this.handleMessage(t)}catch(e){console.error("Error parsing streaming message:",e)}}}finally{t.releaseLock()}}parseSSEEvents(e){let t=[],r=e.split("\n"),s="",n={},a=0;for(;a<r.length;){let e=r[a];""===e?(void 0!==n.data&&t.push(n),n={}):e.startsWith("data: ")?n.data=e.substring(6):e.startsWith("id: ")&&(n.id=e.substring(4)),a++}return r.length>0&&!e.endsWith("\n")&&(s=r[r.length-1]),{events:t,remainder:s}}async disconnect(){if(this.sessionId)try{await fetch(this.config.url,{method:"DELETE",headers:{"Mcp-Session-Id":this.sessionId,...this.config.headers}})}catch(e){console.warn("Failed to terminate session:",e)}this.cleanup(),this.handleDisconnect()}cleanup(){this.sseStream&&(this.sseStream.close(),this.sseStream=null),this.sessionId=null,this.supportsSSE=!1}constructor(e){super(),this.sessionId=null,this.protocolVersion="2025-03-26",this.sseStream=null,this.supportsSSE=!1,this.config=e}}let y={stdio:{filesystem:{type:"stdio",command:"npx",args:["-y","@modelcontextprotocol/server-filesystem","/path/to/directory"]},memory:{type:"stdio",command:"npx",args:["-y","@modelcontextprotocol/server-memory"]},python:{type:"stdio",command:"python",args:["server.py"]}},sse:{local:{type:"sse",url:"http://localhost:3000/sse"},remote:{type:"sse",url:"https://example.com/mcp/sse"}},http:{local:{type:"streamable-http",url:"http://localhost:3000/mcp"},remote:{type:"streamable-http",url:"https://api.example.com/mcp"}}},b=(0,a.createContext)(void 0);function v(e){let{children:t}=e,r=function(){let[e,t]=(0,a.useState)({connections:[],logs:[]}),r=(0,a.useRef)(new Map),s=(0,a.useCallback)((e,r,s,n)=>{let a={timestamp:new Date,level:e,message:r,connectionId:s,data:n};t(e=>({...e,logs:[...e.logs.slice(-99),a]}))},[]),n=(0,a.useCallback)((e,r)=>{t(t=>({...t,connections:t.connections.map(t=>t.id===e?{...t,...r}:t)}))},[]),o=(0,a.useCallback)(async(e,r)=>{let n=d(),a={id:n,name:e,config:r,status:{connected:!1,connecting:!1},tools:[],resources:[],prompts:[]};return t(e=>({...e,connections:[...e.connections,a]})),s("info","Conexi\xf3n creada: ".concat(e),n),n},[s]),i=(0,a.useCallback)(async t=>{let a=e.connections.find(e=>e.id===t);if(!a)throw Error("Conexi\xf3n no encontrada");if(!a.status.connecting&&!a.status.connected){n(t,{status:{...a.status,connecting:!0,error:void 0}}),s("info","Conectando a ".concat(a.name,"..."),t);try{let e=function(e){switch(e.type){case"stdio":return new x(e);case"sse":return new g(e);case"http":case"streamable-http":return new p(e);default:throw Error("Unsupported transport type: ".concat(e.type))}}(a.config),o=new m({name:"MCP Web Client",version:"1.0.0"},{roots:{listChanged:!0},sampling:{}});o.on("initialized",e=>{let r=e.serverInfo;n(t,{status:{connected:!0,connecting:!1,lastConnected:new Date,serverInfo:r}}),s("info","Conectado a ".concat(r.name," v").concat(r.version),t)}),o.on("toolsChanged",e=>{n(t,{tools:e}),s("info","Herramientas actualizadas: ".concat(e.length," disponibles"),t)}),o.on("resourcesChanged",e=>{n(t,{resources:e}),s("info","Recursos actualizados: ".concat(e.length," disponibles"),t)}),o.on("promptsChanged",e=>{n(t,{prompts:e}),s("info","Prompts actualizados: ".concat(e.length," disponibles"),t)}),o.on("toolResult",(e,r)=>{s("info","Herramienta ejecutada: ".concat(e),t,r)}),o.on("log",e=>{s(e.level,e.data,t,e)}),o.on("error",e=>{s("error","Error del cliente: ".concat(e.message),t),n(t,{status:{...a.status,connecting:!1,error:e.message}})}),o.on("disconnect",()=>{s("warn","Desconectado del servidor",t),n(t,{status:{connected:!1,connecting:!1}}),r.current.delete(t)}),await o.connect(e),r.current.set(t,o)}catch(r){let e=r instanceof Error?r.message:"Error desconocido";throw s("error","Error de conexi\xf3n: ".concat(e),t),n(t,{status:{connected:!1,connecting:!1,error:e}}),r}}},[e.connections,n,s]),c=(0,a.useCallback)(async e=>{let t=r.current.get(e);t&&(await t.disconnect(),r.current.delete(e)),n(e,{status:{connected:!1,connecting:!1},tools:[],resources:[],prompts:[]}),s("info","Desconectado manualmente",e)},[n,s]),l=(0,a.useCallback)(async e=>{await c(e),t(t=>({...t,connections:t.connections.filter(t=>t.id!==e),activeConnection:t.activeConnection===e?void 0:t.activeConnection})),s("info","Conexi\xf3n eliminada",e)},[c,s]),h=(0,a.useCallback)(e=>{t(t=>({...t,activeConnection:e}))},[]),u=(0,a.useCallback)(async(e,t)=>{let n=r.current.get(e);if(!n)throw Error("Cliente no encontrado o no conectado");s("info","Ejecutando herramienta: ".concat(t.name),e,t.arguments);try{let r=await n.callTool(t);return s("info","Herramienta completada: ".concat(t.name),e,r),r}catch(n){let r=n instanceof Error?n.message:"Error desconocido";throw s("error","Error ejecutando herramienta ".concat(t.name,": ").concat(r),e),n}},[s]),y=(0,a.useCallback)(async(e,t)=>{let n=r.current.get(e);if(!n)throw Error("Cliente no encontrado o no conectado");s("info","Leyendo recurso: ".concat(t.uri),e);try{let r=await n.readResource(t);return s("info","Recurso le\xeddo: ".concat(t.uri),e),r}catch(n){let r=n instanceof Error?n.message:"Error desconocido";throw s("error","Error leyendo recurso ".concat(t.uri,": ").concat(r),e),n}},[s]),b=(0,a.useCallback)(async(e,t)=>{let n=r.current.get(e);if(!n)throw Error("Cliente no encontrado o no conectado");s("info","Obteniendo prompt: ".concat(t.name),e,t.arguments);try{let r=await n.getPrompt(t);return s("info","Prompt obtenido: ".concat(t.name),e),r}catch(n){let r=n instanceof Error?n.message:"Error desconocido";throw s("error","Error obteniendo prompt ".concat(t.name,": ").concat(r),e),n}},[s]),v=(0,a.useCallback)(()=>{t(e=>({...e,logs:[]}))},[]);return(0,a.useEffect)(()=>()=>{r.current.forEach(async e=>{try{await e.disconnect()}catch(e){console.error("Error disconnecting client:",e)}}),r.current.clear()},[]),{connections:e.connections,activeConnection:e.activeConnection,logs:e.logs,createConnection:o,connectToServer:i,disconnectFromServer:c,removeConnection:l,setActiveConnection:h,callTool:u,readResource:y,getPrompt:b,clearLogs:v,getActiveConnection:()=>e.connections.find(t=>t.id===e.activeConnection),getConnectedClients:()=>e.connections.filter(e=>e.status.connected),isConnected:t=>{let r=e.connections.find(e=>e.id===t);return(null==r?void 0:r.status.connected)||!1}}}();return(0,n.jsx)(b.Provider,{value:r,children:t})}function f(){let e=(0,a.useContext)(b);if(void 0===e)throw Error("useMCPClientContext must be used within a MCPClientProvider");return e}function j(){let{connections:e,activeConnection:t,connectToServer:r,disconnectFromServer:s,removeConnection:o,setActiveConnection:i}=f(),[c,l]=(0,a.useState)(new Set),d=async e=>{l(t=>new Set(t).add(e));try{await r(e)}catch(e){console.error("Error connecting:",e)}finally{l(t=>{let r=new Set(t);return r.delete(e),r})}},h=async e=>{l(t=>new Set(t).add(e));try{await s(e)}catch(e){console.error("Error disconnecting:",e)}finally{l(t=>{let r=new Set(t);return r.delete(e),r})}},m=async e=>{if(confirm("\xbfEst\xe1s seguro de que quieres eliminar esta conexi\xf3n?"))try{await o(e)}catch(e){console.error("Error removing connection:",e)}},u=e=>e.status.connecting?"text-yellow-600 bg-yellow-100 dark:bg-yellow-900 dark:text-yellow-300":e.status.connected?"text-green-600 bg-green-100 dark:bg-green-900 dark:text-green-300":e.status.error?"text-red-600 bg-red-100 dark:bg-red-900 dark:text-red-300":"text-gray-600 bg-gray-100 dark:bg-gray-700 dark:text-gray-300",x=e=>e.status.connecting?"Conectando...":e.status.connected?"Conectado":e.status.error?"Error":"Desconectado",g=e=>{switch(e){case"stdio":return(0,n.jsx)("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,n.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"})});case"sse":return(0,n.jsx)("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,n.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M13 10V3L4 14h7v7l9-11h-7z"})});case"http":case"streamable-http":return(0,n.jsx)("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,n.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9a9 9 0 01-9-9m9 9c1.657 0 3-4.03 3-9s-1.343-9-3-9m0 18c-1.657 0-3-4.03-3-9s1.343-9 3-9m-9 9a9 9 0 019-9"})});default:return(0,n.jsx)("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,n.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M8.111 16.404a5.5 5.5 0 017.778 0M12 20h.01m-7.08-7.071c3.904-3.905 10.236-3.905 14.141 0M1.394 9.393c5.857-5.857 15.355-5.857 21.213 0"})})}};return 0===e.length?(0,n.jsxs)("div",{className:"text-center py-12",children:[(0,n.jsx)("svg",{className:"mx-auto h-12 w-12 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,n.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M8.111 16.404a5.5 5.5 0 017.778 0M12 20h.01m-7.08-7.071c3.904-3.905 10.236-3.905 14.141 0M1.394 9.393c5.857-5.857 15.355-5.857 21.213 0"})}),(0,n.jsx)("h3",{className:"mt-2 text-sm font-medium text-gray-900 dark:text-white",children:"No hay conexiones"}),(0,n.jsx)("p",{className:"mt-1 text-sm text-gray-500 dark:text-gray-400",children:"Crea una nueva conexi\xf3n para comenzar a usar MCP."})]}):(0,n.jsx)("div",{className:"space-y-4",children:e.map(e=>(0,n.jsxs)("div",{className:"border rounded-lg p-4 transition-all ".concat(t===e.id?"border-blue-500 bg-blue-50 dark:bg-blue-900/20":"border-gray-200 dark:border-gray-700 hover:border-gray-300 dark:hover:border-gray-600"),children:[(0,n.jsxs)("div",{className:"flex items-center justify-between",children:[(0,n.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,n.jsxs)("div",{className:"flex items-center space-x-2",children:[g(e.config.type),(0,n.jsx)("h3",{className:"text-lg font-medium text-gray-900 dark:text-white",children:e.name})]}),(0,n.jsx)("span",{className:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ".concat(u(e)),children:x(e)})]}),(0,n.jsxs)("div",{className:"flex items-center space-x-2",children:[!e.status.connected&&!e.status.connecting&&(0,n.jsx)("button",{onClick:()=>d(e.id),disabled:c.has(e.id),className:"bg-green-600 hover:bg-green-700 disabled:bg-green-400 text-white px-3 py-1 rounded text-sm font-medium transition-colors",children:c.has(e.id)?"Conectando...":"Conectar"}),e.status.connected&&(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)("button",{onClick:()=>i(e.id),className:"px-3 py-1 rounded text-sm font-medium transition-colors ".concat(t===e.id?"bg-blue-600 text-white":"bg-gray-200 hover:bg-gray-300 text-gray-700 dark:bg-gray-600 dark:hover:bg-gray-500 dark:text-gray-200"),children:t===e.id?"Activa":"Activar"}),(0,n.jsx)("button",{onClick:()=>h(e.id),disabled:c.has(e.id),className:"bg-red-600 hover:bg-red-700 disabled:bg-red-400 text-white px-3 py-1 rounded text-sm font-medium transition-colors",children:c.has(e.id)?"Desconectando...":"Desconectar"})]}),(0,n.jsx)("button",{onClick:()=>m(e.id),className:"text-gray-400 hover:text-red-600 dark:hover:text-red-400 p-1",title:"Eliminar conexi\xf3n",children:(0,n.jsx)("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,n.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"})})})]})]}),(0,n.jsxs)("div",{className:"mt-3 text-sm text-gray-600 dark:text-gray-300",children:[(0,n.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-2",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("span",{className:"font-medium",children:"Tipo:"})," ",e.config.type.toUpperCase()]}),e.config.url&&(0,n.jsxs)("div",{children:[(0,n.jsx)("span",{className:"font-medium",children:"URL:"})," ",e.config.url]}),e.config.command&&(0,n.jsxs)("div",{children:[(0,n.jsx)("span",{className:"font-medium",children:"Comando:"})," ",e.config.command,e.config.args&&e.config.args.length>0&&(0,n.jsx)("span",{className:"ml-1",children:e.config.args.join(" ")})]}),e.status.serverInfo&&(0,n.jsxs)("div",{children:[(0,n.jsx)("span",{className:"font-medium",children:"Servidor:"})," ",e.status.serverInfo.name," v",e.status.serverInfo.version]}),e.status.lastConnected&&(0,n.jsxs)("div",{children:[(0,n.jsx)("span",{className:"font-medium",children:"\xdaltima conexi\xf3n:"})," ",e.status.lastConnected.toLocaleString()]})]}),e.status.error&&(0,n.jsxs)("div",{className:"mt-2 p-2 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded text-red-700 dark:text-red-300 text-xs",children:[(0,n.jsx)("span",{className:"font-medium",children:"Error:"})," ",e.status.error]}),e.status.connected&&(0,n.jsxs)("div",{className:"mt-2 flex space-x-4 text-xs",children:[(0,n.jsxs)("span",{children:[(0,n.jsx)("span",{className:"font-medium",children:"Herramientas:"})," ",e.tools.length]}),(0,n.jsxs)("span",{children:[(0,n.jsx)("span",{className:"font-medium",children:"Recursos:"})," ",e.resources.length]}),(0,n.jsxs)("span",{children:[(0,n.jsx)("span",{className:"font-medium",children:"Prompts:"})," ",e.prompts.length]})]})]})]},e.id))})}function k(e){let{onSuccess:t,onCancel:r}=e,{createConnection:s,connectToServer:o}=f(),[i,c]=(0,a.useState)({name:"",type:"stdio",url:"",command:"",args:"",env:""}),[l,d]=(0,a.useState)(!1),[h,m]=(0,a.useState)(null),u=async e=>{e.preventDefault(),d(!0),m(null);try{if(!i.name.trim())throw Error("El nombre es requerido");let e={type:i.type};if("stdio"===i.type){if(!i.command.trim())throw Error("El comando es requerido para conexiones STDIO");e.command=i.command.trim(),e.args=i.args.trim()?i.args.trim().split(" "):[],e.env=i.env.trim()?JSON.parse(i.env):{}}else{if(!i.url.trim())throw Error("La URL es requerida para conexiones HTTP/SSE");e.url=i.url.trim()}let r=await s(i.name.trim(),e);try{await o(r)}catch(e){console.warn("Failed to auto-connect:",e)}t()}catch(e){m(e instanceof Error?e.message:"Error desconocido")}finally{d(!1)}},x=e=>{c({name:"",type:e.type,url:e.url||"",command:e.command||"",args:e.args?e.args.join(" "):"",env:e.env?JSON.stringify(e.env,null,2):""})};return(0,n.jsxs)("form",{onSubmit:u,className:"space-y-6",children:[h&&(0,n.jsx)("div",{className:"bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md p-4",children:(0,n.jsx)("div",{className:"text-red-700 dark:text-red-300 text-sm",children:h})}),(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{htmlFor:"name",className:"block text-sm font-medium text-gray-700 dark:text-gray-300",children:"Nombre de la conexi\xf3n"}),(0,n.jsx)("input",{type:"text",id:"name",value:i.name,onChange:e=>c({...i,name:e.target.value}),className:"mt-1 block w-full border border-gray-300 dark:border-gray-600 rounded-md px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"Mi servidor MCP",required:!0})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{htmlFor:"type",className:"block text-sm font-medium text-gray-700 dark:text-gray-300",children:"Tipo de transporte"}),(0,n.jsxs)("select",{id:"type",value:i.type,onChange:e=>c({...i,type:e.target.value}),className:"mt-1 block w-full border border-gray-300 dark:border-gray-600 rounded-md px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500",children:[(0,n.jsx)("option",{value:"stdio",children:"STDIO (Proceso local)"}),(0,n.jsx)("option",{value:"sse",children:"SSE (Server-Sent Events)"}),(0,n.jsx)("option",{value:"streamable-http",children:"HTTP Streamable"})]})]}),"stdio"===i.type?(0,n.jsxs)(n.Fragment,{children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{htmlFor:"command",className:"block text-sm font-medium text-gray-700 dark:text-gray-300",children:"Comando"}),(0,n.jsx)("input",{type:"text",id:"command",value:i.command,onChange:e=>c({...i,command:e.target.value}),className:"mt-1 block w-full border border-gray-300 dark:border-gray-600 rounded-md px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"npx, python, node, etc.",required:!0})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{htmlFor:"args",className:"block text-sm font-medium text-gray-700 dark:text-gray-300",children:"Argumentos (separados por espacios)"}),(0,n.jsx)("input",{type:"text",id:"args",value:i.args,onChange:e=>c({...i,args:e.target.value}),className:"mt-1 block w-full border border-gray-300 dark:border-gray-600 rounded-md px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"-y @modelcontextprotocol/server-memory"})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{htmlFor:"env",className:"block text-sm font-medium text-gray-700 dark:text-gray-300",children:"Variables de entorno (JSON)"}),(0,n.jsx)("textarea",{id:"env",value:i.env,onChange:e=>c({...i,env:e.target.value}),rows:3,className:"mt-1 block w-full border border-gray-300 dark:border-gray-600 rounded-md px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:'{"API_KEY": "valor"}'})]})]}):(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{htmlFor:"url",className:"block text-sm font-medium text-gray-700 dark:text-gray-300",children:"URL del servidor"}),(0,n.jsx)("input",{type:"url",id:"url",value:i.url,onChange:e=>c({...i,url:e.target.value}),className:"mt-1 block w-full border border-gray-300 dark:border-gray-600 rounded-md px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"http://localhost:3000/mcp",required:!0})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Configuraciones predefinidas"}),(0,n.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-2",children:["stdio"===i.type&&(0,n.jsxs)(n.Fragment,{children:[(0,n.jsxs)("button",{type:"button",onClick:()=>x(y.stdio.filesystem),className:"text-left p-2 border border-gray-300 dark:border-gray-600 rounded hover:bg-gray-50 dark:hover:bg-gray-700 text-sm",children:[(0,n.jsx)("div",{className:"font-medium",children:"Filesystem"}),(0,n.jsx)("div",{className:"text-gray-500 dark:text-gray-400",children:"Acceso al sistema de archivos"})]}),(0,n.jsxs)("button",{type:"button",onClick:()=>x(y.stdio.memory),className:"text-left p-2 border border-gray-300 dark:border-gray-600 rounded hover:bg-gray-50 dark:hover:bg-gray-700 text-sm",children:[(0,n.jsx)("div",{className:"font-medium",children:"Memory"}),(0,n.jsx)("div",{className:"text-gray-500 dark:text-gray-400",children:"Almacenamiento en memoria"})]})]}),("sse"===i.type||"streamable-http"===i.type)&&(0,n.jsx)(n.Fragment,{children:(0,n.jsxs)("button",{type:"button",onClick:()=>x("sse"===i.type?y.sse.local:y.http.local),className:"text-left p-2 border border-gray-300 dark:border-gray-600 rounded hover:bg-gray-50 dark:hover:bg-gray-700 text-sm",children:[(0,n.jsx)("div",{className:"font-medium",children:"Servidor local"}),(0,n.jsx)("div",{className:"text-gray-500 dark:text-gray-400",children:"localhost:3000"})]})})]})]}),(0,n.jsxs)("div",{className:"flex justify-end space-x-3",children:[(0,n.jsx)("button",{type:"button",onClick:r,className:"px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors",children:"Cancelar"}),(0,n.jsx)("button",{type:"submit",disabled:l,className:"px-4 py-2 bg-blue-600 hover:bg-blue-700 disabled:bg-blue-400 text-white rounded-md transition-colors",children:l?"Creando...":"Crear Conexi\xf3n"})]})]})}function N(){let{getActiveConnection:e,callTool:t}=f(),[r,s]=(0,a.useState)(null),[o,i]=(0,a.useState)({}),[c,l]=(0,a.useState)(!1),[d,h]=(0,a.useState)(null),[m,u]=(0,a.useState)(null),x=e(),g=(null==x?void 0:x.tools)||[],p=e=>{s(e),h(null),u(null);let t={};e.inputSchema.properties&&Object.keys(e.inputSchema.properties).forEach(r=>{let s=e.inputSchema.properties[r];void 0!==s.default?t[r]=s.default:"string"===s.type?t[r]="":"number"===s.type?t[r]=0:"boolean"===s.type&&(t[r]=!1)}),i(t)},y=async()=>{if(r&&x){l(!0),u(null),h(null);try{let e={name:r.name,arguments:o},s=await t(x.id,e);h(s)}catch(e){u(e instanceof Error?e.message:"Error desconocido")}finally{l(!1)}}},b=(e,t)=>{var s;let a=(null==r||null==(s=r.inputSchema.required)?void 0:s.includes(e))||!1;switch(t.type){case"string":if(t.enum)return(0,n.jsxs)("select",{value:o[e]||"",onChange:t=>i({...o,[e]:t.target.value}),className:"mt-1 block w-full border border-gray-300 dark:border-gray-600 rounded-md px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500",required:a,children:[(0,n.jsx)("option",{value:"",children:"Seleccionar..."}),t.enum.map(e=>(0,n.jsx)("option",{value:e,children:e},e))]});return(0,n.jsx)("input",{type:"text",value:o[e]||"",onChange:t=>i({...o,[e]:t.target.value}),className:"mt-1 block w-full border border-gray-300 dark:border-gray-600 rounded-md px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:t.description||"Ingrese ".concat(e),required:a});case"number":return(0,n.jsx)("input",{type:"number",value:o[e]||"",onChange:t=>i({...o,[e]:parseFloat(t.target.value)||0}),className:"mt-1 block w-full border border-gray-300 dark:border-gray-600 rounded-md px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:t.description||"Ingrese ".concat(e),required:a});case"boolean":return(0,n.jsxs)("label",{className:"flex items-center mt-1",children:[(0,n.jsx)("input",{type:"checkbox",checked:o[e]||!1,onChange:t=>i({...o,[e]:t.target.checked}),className:"rounded border-gray-300 dark:border-gray-600 text-blue-600 focus:ring-blue-500"}),(0,n.jsx)("span",{className:"ml-2 text-sm text-gray-700 dark:text-gray-300",children:t.description||e})]});default:return(0,n.jsx)("textarea",{value:"object"==typeof o[e]?JSON.stringify(o[e],null,2):o[e]||"",onChange:t=>{try{let r=t.target.value;i({...o,[e]:r.startsWith("{")||r.startsWith("[")?JSON.parse(r):r})}catch(r){i({...o,[e]:t.target.value})}},rows:3,className:"mt-1 block w-full border border-gray-300 dark:border-gray-600 rounded-md px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:t.description||"Ingrese ".concat(e," (JSON si es objeto/array)"),required:a})}};return x?0===g.length?(0,n.jsxs)("div",{className:"text-center py-8",children:[(0,n.jsxs)("svg",{className:"mx-auto h-12 w-12 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:[(0,n.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"}),(0,n.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M15 12a3 3 0 11-6 0 3 3 0 016 0z"})]}),(0,n.jsx)("h3",{className:"mt-2 text-sm font-medium text-gray-900 dark:text-white",children:"No hay herramientas disponibles"}),(0,n.jsx)("p",{className:"mt-1 text-sm text-gray-500 dark:text-gray-400",children:"El servidor conectado no expone ninguna herramienta."})]}):(0,n.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,n.jsxs)("div",{children:[(0,n.jsxs)("h3",{className:"text-lg font-medium text-gray-900 dark:text-white mb-4",children:["Herramientas Disponibles (",g.length,")"]}),(0,n.jsx)("div",{className:"space-y-3",children:g.map(e=>(0,n.jsxs)("div",{onClick:()=>p(e),className:"p-4 border rounded-lg cursor-pointer transition-all ".concat((null==r?void 0:r.name)===e.name?"border-blue-500 bg-blue-50 dark:bg-blue-900/20":"border-gray-200 dark:border-gray-700 hover:border-gray-300 dark:hover:border-gray-600"),children:[(0,n.jsx)("h4",{className:"font-medium text-gray-900 dark:text-white",children:e.name}),e.description&&(0,n.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-300 mt-1",children:e.description}),e.inputSchema.required&&e.inputSchema.required.length>0&&(0,n.jsx)("div",{className:"mt-2",children:(0,n.jsxs)("span",{className:"text-xs text-gray-500 dark:text-gray-400",children:["Par\xe1metros requeridos: ",e.inputSchema.required.join(", ")]})})]},e.name))})]}),(0,n.jsx)("div",{children:r?(0,n.jsxs)("div",{children:[(0,n.jsxs)("h3",{className:"text-lg font-medium text-gray-900 dark:text-white mb-4",children:["Ejecutar: ",r.name]}),r.description&&(0,n.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-300 mb-4",children:r.description}),r.inputSchema.properties&&Object.keys(r.inputSchema.properties).length>0&&(0,n.jsxs)("div",{className:"space-y-4 mb-6",children:[(0,n.jsx)("h4",{className:"font-medium text-gray-900 dark:text-white",children:"Par\xe1metros"}),Object.entries(r.inputSchema.properties).map(e=>{var t;let[s,a]=e;return(0,n.jsxs)("div",{children:[(0,n.jsxs)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300",children:[s,(null==(t=r.inputSchema.required)?void 0:t.includes(s))&&(0,n.jsx)("span",{className:"text-red-500 ml-1",children:"*"})]}),b(s,a),a.description&&(0,n.jsx)("p",{className:"mt-1 text-xs text-gray-500 dark:text-gray-400",children:a.description})]},s)})]}),(0,n.jsx)("button",{onClick:y,disabled:c,className:"w-full bg-blue-600 hover:bg-blue-700 disabled:bg-blue-400 text-white py-2 px-4 rounded-lg font-medium transition-colors",children:c?"Ejecutando...":"Ejecutar Herramienta"}),m&&(0,n.jsxs)("div",{className:"mt-4 p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg",children:[(0,n.jsx)("h4",{className:"font-medium text-red-800 dark:text-red-300 mb-2",children:"Error"}),(0,n.jsx)("p",{className:"text-sm text-red-700 dark:text-red-300",children:m})]}),d&&(0,n.jsxs)("div",{className:"mt-4 p-4 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg",children:[(0,n.jsx)("h4",{className:"font-medium text-green-800 dark:text-green-300 mb-2",children:"Resultado"}),(0,n.jsx)("div",{className:"space-y-2",children:d.content.map((e,t)=>(0,n.jsxs)("div",{className:"text-sm",children:["text"===e.type&&(0,n.jsx)("pre",{className:"whitespace-pre-wrap text-green-700 dark:text-green-300 bg-green-100 dark:bg-green-900/40 p-2 rounded",children:e.text}),"image"===e.type&&(0,n.jsxs)("div",{children:[(0,n.jsx)("p",{className:"text-green-700 dark:text-green-300 mb-1",children:"Imagen:"}),(0,n.jsx)("img",{src:"data:".concat(e.mimeType,";base64,").concat(e.data),alt:"Tool result",className:"max-w-full h-auto rounded"})]}),"resource"===e.type&&(0,n.jsx)("div",{children:(0,n.jsxs)("p",{className:"text-green-700 dark:text-green-300",children:["Recurso: ",e.text]})})]},t))}),d.isError&&(0,n.jsx)("p",{className:"text-xs text-red-600 dark:text-red-400 mt-2",children:"⚠️ La herramienta report\xf3 un error"})]})]}):(0,n.jsx)("div",{className:"text-center py-8",children:(0,n.jsx)("p",{className:"text-gray-500 dark:text-gray-400",children:"Selecciona una herramienta para ejecutarla."})})})]}):(0,n.jsx)("div",{className:"text-center py-8",children:(0,n.jsx)("p",{className:"text-gray-500 dark:text-gray-400",children:"Selecciona una conexi\xf3n activa para ver las herramientas disponibles."})})}function w(){let{getActiveConnection:e,readResource:t}=f(),[r,s]=(0,a.useState)(null),[o,i]=(0,a.useState)(!1),[c,l]=(0,a.useState)(null),[d,h]=(0,a.useState)(null),m=e(),u=(null==m?void 0:m.resources)||[],x=async e=>{if(m){s(e),i(!0),h(null),l(null);try{let r=await t(m.id,{uri:e.uri});l(r)}catch(e){h(e instanceof Error?e.message:"Error desconocido")}finally{i(!1)}}};return m?0===u.length?(0,n.jsxs)("div",{className:"text-center py-8",children:[(0,n.jsx)("svg",{className:"mx-auto h-12 w-12 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,n.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"})}),(0,n.jsx)("h3",{className:"mt-2 text-sm font-medium text-gray-900 dark:text-white",children:"No hay recursos disponibles"}),(0,n.jsx)("p",{className:"mt-1 text-sm text-gray-500 dark:text-gray-400",children:"El servidor conectado no expone ning\xfan recurso."})]}):(0,n.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,n.jsxs)("div",{children:[(0,n.jsxs)("h3",{className:"text-lg font-medium text-gray-900 dark:text-white mb-4",children:["Recursos Disponibles (",u.length,")"]}),(0,n.jsx)("div",{className:"space-y-3",children:u.map(e=>(0,n.jsxs)("div",{onClick:()=>x(e),className:"p-4 border rounded-lg cursor-pointer transition-all ".concat((null==r?void 0:r.uri)===e.uri?"border-blue-500 bg-blue-50 dark:bg-blue-900/20":"border-gray-200 dark:border-gray-700 hover:border-gray-300 dark:hover:border-gray-600"),children:[(0,n.jsx)("h4",{className:"font-medium text-gray-900 dark:text-white",children:e.name}),(0,n.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-300 mt-1 font-mono",children:e.uri}),e.description&&(0,n.jsx)("p",{className:"text-sm text-gray-500 dark:text-gray-400 mt-1",children:e.description}),e.mimeType&&(0,n.jsx)("span",{className:"inline-block mt-2 px-2 py-1 bg-gray-100 dark:bg-gray-700 text-xs text-gray-600 dark:text-gray-300 rounded",children:e.mimeType})]},e.uri))})]}),(0,n.jsx)("div",{children:r?(0,n.jsxs)("div",{children:[(0,n.jsx)("h3",{className:"text-lg font-medium text-gray-900 dark:text-white mb-4",children:r.name}),(0,n.jsxs)("div",{className:"mb-4 p-3 bg-gray-50 dark:bg-gray-700 rounded-lg",children:[(0,n.jsxs)("p",{className:"text-sm text-gray-600 dark:text-gray-300",children:[(0,n.jsx)("span",{className:"font-medium",children:"URI:"})," ",r.uri]}),r.mimeType&&(0,n.jsxs)("p",{className:"text-sm text-gray-600 dark:text-gray-300 mt-1",children:[(0,n.jsx)("span",{className:"font-medium",children:"Tipo:"})," ",r.mimeType]}),r.description&&(0,n.jsxs)("p",{className:"text-sm text-gray-600 dark:text-gray-300 mt-1",children:[(0,n.jsx)("span",{className:"font-medium",children:"Descripci\xf3n:"})," ",r.description]})]}),o&&(0,n.jsxs)("div",{className:"text-center py-8",children:[(0,n.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"}),(0,n.jsx)("p",{className:"text-sm text-gray-500 dark:text-gray-400 mt-2",children:"Cargando recurso..."})]}),d&&(0,n.jsxs)("div",{className:"p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg",children:[(0,n.jsx)("h4",{className:"font-medium text-red-800 dark:text-red-300 mb-2",children:"Error"}),(0,n.jsx)("p",{className:"text-sm text-red-700 dark:text-red-300",children:d})]}),c&&(0,n.jsx)("div",{className:"space-y-4",children:c.contents.map((e,t)=>{var s,a,o;return(0,n.jsxs)("div",{className:"border border-gray-200 dark:border-gray-700 rounded-lg overflow-hidden",children:[(0,n.jsx)("div",{className:"bg-gray-50 dark:bg-gray-700 px-4 py-2 border-b border-gray-200 dark:border-gray-600",children:(0,n.jsxs)("p",{className:"text-sm font-medium text-gray-900 dark:text-white",children:["Contenido ",t+1,e.mimeType&&(0,n.jsxs)("span",{className:"ml-2 text-xs text-gray-500 dark:text-gray-400",children:["(",e.mimeType,")"]})]})}),(0,n.jsxs)("div",{className:"p-4",children:[e.text&&(0,n.jsx)("div",{children:(null==(s=e.mimeType)?void 0:s.includes("json"))?(0,n.jsx)("pre",{className:"text-sm bg-gray-100 dark:bg-gray-800 p-3 rounded overflow-x-auto",children:JSON.stringify(JSON.parse(e.text),null,2)}):(null==(a=e.mimeType)?void 0:a.includes("html"))?(0,n.jsx)("div",{className:"prose dark:prose-invert max-w-none",children:(0,n.jsx)("div",{dangerouslySetInnerHTML:{__html:e.text}})}):(0,n.jsx)("pre",{className:"text-sm bg-gray-100 dark:bg-gray-800 p-3 rounded overflow-x-auto whitespace-pre-wrap",children:e.text})}),e.blob&&(0,n.jsx)("div",{children:(null==(o=e.mimeType)?void 0:o.startsWith("image/"))?(0,n.jsx)("img",{src:"data:".concat(e.mimeType,";base64,").concat(e.blob),alt:"Resource content",className:"max-w-full h-auto rounded"}):(0,n.jsxs)("div",{className:"text-center py-4",children:[(0,n.jsxs)("p",{className:"text-sm text-gray-500 dark:text-gray-400",children:["Contenido binario (",e.mimeType,")"]}),(0,n.jsx)("button",{onClick:()=>{let t=document.createElement("a");t.href="data:".concat(e.mimeType,";base64,").concat(e.blob),t.download=r.name||"resource",t.click()},className:"mt-2 bg-blue-600 hover:bg-blue-700 text-white px-3 py-1 rounded text-sm",children:"Descargar"})]})})]})]},t)})})]}):(0,n.jsx)("div",{className:"text-center py-8",children:(0,n.jsx)("p",{className:"text-gray-500 dark:text-gray-400",children:"Selecciona un recurso para ver su contenido."})})})]}):(0,n.jsx)("div",{className:"text-center py-8",children:(0,n.jsx)("p",{className:"text-gray-500 dark:text-gray-400",children:"Selecciona una conexi\xf3n activa para ver los recursos disponibles."})})}function S(){let{getActiveConnection:e,getPrompt:t}=f(),[r,s]=(0,a.useState)(null),[o,i]=(0,a.useState)({}),[c,l]=(0,a.useState)(!1),[d,h]=(0,a.useState)(null),[m,u]=(0,a.useState)(null),x=e(),g=(null==x?void 0:x.prompts)||[],p=e=>{s(e),h(null),u(null);let t={};e.arguments&&e.arguments.forEach(e=>{t[e.name]=""}),i(t)},y=async()=>{if(r&&x){l(!0),u(null),h(null);try{let e=await t(x.id,{name:r.name,arguments:o});h(e)}catch(e){u(e instanceof Error?e.message:"Error desconocido")}finally{l(!1)}}};return x?0===g.length?(0,n.jsxs)("div",{className:"text-center py-8",children:[(0,n.jsx)("svg",{className:"mx-auto h-12 w-12 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,n.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"})}),(0,n.jsx)("h3",{className:"mt-2 text-sm font-medium text-gray-900 dark:text-white",children:"No hay prompts disponibles"}),(0,n.jsx)("p",{className:"mt-1 text-sm text-gray-500 dark:text-gray-400",children:"El servidor conectado no expone ning\xfan prompt."})]}):(0,n.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,n.jsxs)("div",{children:[(0,n.jsxs)("h3",{className:"text-lg font-medium text-gray-900 dark:text-white mb-4",children:["Prompts Disponibles (",g.length,")"]}),(0,n.jsx)("div",{className:"space-y-3",children:g.map(e=>(0,n.jsxs)("div",{onClick:()=>p(e),className:"p-4 border rounded-lg cursor-pointer transition-all ".concat((null==r?void 0:r.name)===e.name?"border-blue-500 bg-blue-50 dark:bg-blue-900/20":"border-gray-200 dark:border-gray-700 hover:border-gray-300 dark:hover:border-gray-600"),children:[(0,n.jsx)("h4",{className:"font-medium text-gray-900 dark:text-white",children:e.name}),e.description&&(0,n.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-300 mt-1",children:e.description}),e.arguments&&e.arguments.length>0&&(0,n.jsx)("div",{className:"mt-2",children:(0,n.jsxs)("span",{className:"text-xs text-gray-500 dark:text-gray-400",children:["Argumentos: ",e.arguments.map(e=>e.name).join(", ")]})})]},e.name))})]}),(0,n.jsx)("div",{children:r?(0,n.jsxs)("div",{children:[(0,n.jsxs)("h3",{className:"text-lg font-medium text-gray-900 dark:text-white mb-4",children:["Prompt: ",r.name]}),r.description&&(0,n.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-300 mb-4",children:r.description}),r.arguments&&r.arguments.length>0&&(0,n.jsxs)("div",{className:"space-y-4 mb-6",children:[(0,n.jsx)("h4",{className:"font-medium text-gray-900 dark:text-white",children:"Argumentos"}),r.arguments.map(e=>(0,n.jsxs)("div",{children:[(0,n.jsxs)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300",children:[e.name,e.required&&(0,n.jsx)("span",{className:"text-red-500 ml-1",children:"*"})]}),(0,n.jsx)("input",{type:"text",value:o[e.name]||"",onChange:t=>i({...o,[e.name]:t.target.value}),className:"mt-1 block w-full border border-gray-300 dark:border-gray-600 rounded-md px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:e.description||"Ingrese ".concat(e.name),required:e.required}),e.description&&(0,n.jsx)("p",{className:"mt-1 text-xs text-gray-500 dark:text-gray-400",children:e.description})]},e.name))]}),(0,n.jsx)("button",{onClick:y,disabled:c,className:"w-full bg-blue-600 hover:bg-blue-700 disabled:bg-blue-400 text-white py-2 px-4 rounded-lg font-medium transition-colors",children:c?"Generando...":"Generar Prompt"}),m&&(0,n.jsxs)("div",{className:"mt-4 p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg",children:[(0,n.jsx)("h4",{className:"font-medium text-red-800 dark:text-red-300 mb-2",children:"Error"}),(0,n.jsx)("p",{className:"text-sm text-red-700 dark:text-red-300",children:m})]}),d&&(0,n.jsxs)("div",{className:"mt-4 space-y-4",children:[d.description&&(0,n.jsxs)("div",{className:"p-3 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg",children:[(0,n.jsx)("h4",{className:"font-medium text-blue-800 dark:text-blue-300 mb-1",children:"Descripci\xf3n"}),(0,n.jsx)("p",{className:"text-sm text-blue-700 dark:text-blue-300",children:d.description})]}),(0,n.jsxs)("div",{className:"p-4 bg-gray-50 dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg",children:[(0,n.jsx)("h4",{className:"font-medium text-gray-900 dark:text-white mb-3",children:"Mensajes Generados"}),(0,n.jsx)("div",{className:"space-y-3",children:d.messages.map((e,t)=>(0,n.jsxs)("div",{className:"border border-gray-200 dark:border-gray-600 rounded-lg overflow-hidden",children:[(0,n.jsx)("div",{className:"bg-gray-100 dark:bg-gray-700 px-3 py-2",children:(0,n.jsx)("span",{className:"text-sm font-medium ".concat("user"===e.role?"text-blue-600 dark:text-blue-400":"assistant"===e.role?"text-green-600 dark:text-green-400":"text-purple-600 dark:text-purple-400"),children:e.role.charAt(0).toUpperCase()+e.role.slice(1)})}),(0,n.jsxs)("div",{className:"p-3",children:["text"===e.content.type&&(0,n.jsx)("p",{className:"text-sm text-gray-900 dark:text-white whitespace-pre-wrap",children:e.content.text}),"image"===e.content.type&&(0,n.jsxs)("div",{children:[(0,n.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-300 mb-2",children:"Imagen:"}),(0,n.jsx)("img",{src:"data:".concat(e.content.mimeType,";base64,").concat(e.content.data),alt:"Prompt content",className:"max-w-full h-auto rounded"})]}),"resource"===e.content.type&&(0,n.jsx)("div",{children:(0,n.jsxs)("p",{className:"text-sm text-gray-600 dark:text-gray-300",children:["Recurso: ",e.content.text]})})]})]},t))})]})]})]}):(0,n.jsx)("div",{className:"text-center py-8",children:(0,n.jsx)("p",{className:"text-gray-500 dark:text-gray-400",children:"Selecciona un prompt para generarlo."})})})]}):(0,n.jsx)("div",{className:"text-center py-8",children:(0,n.jsx)("p",{className:"text-gray-500 dark:text-gray-400",children:"Selecciona una conexi\xf3n activa para ver los prompts disponibles."})})}function E(){let{logs:e,clearLogs:t,connections:r}=f(),[s,o]=(0,a.useState)({level:"all",connectionId:"all",search:""}),[i,c]=(0,a.useState)(!0),l=(0,a.useRef)(null);(0,a.useEffect)(()=>{i&&l.current&&l.current.scrollIntoView({behavior:"smooth"})},[e,i]);let d=e.filter(e=>("all"===s.level||e.level===s.level)&&("all"===s.connectionId||e.connectionId===s.connectionId)&&(!s.search||!!e.message.toLowerCase().includes(s.search.toLowerCase()))),h=e=>{switch(e){case"error":return"text-red-600 bg-red-100 dark:bg-red-900/20 dark:text-red-300";case"warn":return"text-yellow-600 bg-yellow-100 dark:bg-yellow-900/20 dark:text-yellow-300";case"info":return"text-blue-600 bg-blue-100 dark:bg-blue-900/20 dark:text-blue-300";default:return"text-gray-600 bg-gray-100 dark:bg-gray-700 dark:text-gray-300"}},m=e=>{switch(e){case"error":return(0,n.jsx)("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,n.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})});case"warn":return(0,n.jsx)("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,n.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"})});case"info":return(0,n.jsx)("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,n.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})});case"debug":return(0,n.jsxs)("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:[(0,n.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"}),(0,n.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M15 12a3 3 0 11-6 0 3 3 0 016 0z"})]});default:return null}},u=e=>{if(!e)return"Sistema";let t=r.find(t=>t.id===e);return(null==t?void 0:t.name)||"Conexi\xf3n desconocida"};return(0,n.jsxs)("div",{className:"space-y-4",children:[(0,n.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between",children:[(0,n.jsxs)("div",{className:"flex flex-col sm:flex-row gap-3 flex-1",children:[(0,n.jsxs)("select",{value:s.level,onChange:e=>o({...s,level:e.target.value}),className:"border border-gray-300 dark:border-gray-600 rounded-md px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white text-sm",children:[(0,n.jsx)("option",{value:"all",children:"Todos los niveles"}),(0,n.jsx)("option",{value:"error",children:"Error"}),(0,n.jsx)("option",{value:"warn",children:"Advertencia"}),(0,n.jsx)("option",{value:"info",children:"Informaci\xf3n"}),(0,n.jsx)("option",{value:"debug",children:"Debug"})]}),(0,n.jsxs)("select",{value:s.connectionId,onChange:e=>o({...s,connectionId:e.target.value}),className:"border border-gray-300 dark:border-gray-600 rounded-md px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white text-sm",children:[(0,n.jsx)("option",{value:"all",children:"Todas las conexiones"}),(0,n.jsx)("option",{value:"",children:"Sistema"}),r.map(e=>(0,n.jsx)("option",{value:e.id,children:e.name},e.id))]}),(0,n.jsx)("input",{type:"text",placeholder:"Buscar en logs...",value:s.search,onChange:e=>o({...s,search:e.target.value}),className:"border border-gray-300 dark:border-gray-600 rounded-md px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white text-sm flex-1 min-w-0"})]}),(0,n.jsxs)("div",{className:"flex items-center gap-2",children:[(0,n.jsxs)("label",{className:"flex items-center text-sm text-gray-600 dark:text-gray-300",children:[(0,n.jsx)("input",{type:"checkbox",checked:i,onChange:e=>c(e.target.checked),className:"rounded border-gray-300 dark:border-gray-600 text-blue-600 focus:ring-blue-500 mr-2"}),"Auto-scroll"]}),(0,n.jsx)("button",{onClick:t,className:"bg-red-600 hover:bg-red-700 text-white px-3 py-2 rounded-md text-sm font-medium transition-colors",children:"Limpiar"})]})]}),(0,n.jsx)("div",{className:"bg-gray-50 dark:bg-gray-800 rounded-lg p-4",children:(0,n.jsxs)("div",{className:"grid grid-cols-2 sm:grid-cols-5 gap-4 text-center",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("div",{className:"text-lg font-semibold text-gray-900 dark:text-white",children:e.length}),(0,n.jsx)("div",{className:"text-xs text-gray-500 dark:text-gray-400",children:"Total"})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("div",{className:"text-lg font-semibold text-red-600",children:e.filter(e=>"error"===e.level).length}),(0,n.jsx)("div",{className:"text-xs text-gray-500 dark:text-gray-400",children:"Errores"})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("div",{className:"text-lg font-semibold text-yellow-600",children:e.filter(e=>"warn"===e.level).length}),(0,n.jsx)("div",{className:"text-xs text-gray-500 dark:text-gray-400",children:"Advertencias"})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("div",{className:"text-lg font-semibold text-blue-600",children:e.filter(e=>"info"===e.level).length}),(0,n.jsx)("div",{className:"text-xs text-gray-500 dark:text-gray-400",children:"Info"})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("div",{className:"text-lg font-semibold text-gray-600",children:e.filter(e=>"debug"===e.level).length}),(0,n.jsx)("div",{className:"text-xs text-gray-500 dark:text-gray-400",children:"Debug"})]})]})}),(0,n.jsxs)("div",{className:"bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg",children:[(0,n.jsx)("div",{className:"max-h-96 overflow-y-auto",children:0===d.length?(0,n.jsx)("div",{className:"text-center py-8",children:(0,n.jsx)("p",{className:"text-gray-500 dark:text-gray-400",children:0===e.length?"No hay logs disponibles":"No hay logs que coincidan con los filtros"})}):(0,n.jsx)("div",{className:"divide-y divide-gray-200 dark:divide-gray-700",children:d.map((e,t)=>(0,n.jsx)("div",{className:"p-4 hover:bg-gray-50 dark:hover:bg-gray-700/50",children:(0,n.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,n.jsx)("div",{className:"flex-shrink-0 p-1 rounded ".concat(h(e.level)),children:m(e.level)}),(0,n.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,n.jsxs)("div",{className:"flex items-center justify-between",children:[(0,n.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,n.jsx)("span",{className:"inline-flex items-center px-2 py-0.5 rounded text-xs font-medium ".concat(h(e.level)),children:e.level.toUpperCase()}),(0,n.jsx)("span",{className:"text-xs text-gray-500 dark:text-gray-400",children:u(e.connectionId)})]}),(0,n.jsx)("span",{className:"text-xs text-gray-500 dark:text-gray-400",children:e.timestamp.toLocaleTimeString()})]}),(0,n.jsx)("p",{className:"mt-1 text-sm text-gray-900 dark:text-white",children:e.message}),e.data&&(0,n.jsxs)("details",{className:"mt-2",children:[(0,n.jsx)("summary",{className:"text-xs text-gray-500 dark:text-gray-400 cursor-pointer hover:text-gray-700 dark:hover:text-gray-300",children:"Ver datos adicionales"}),(0,n.jsx)("pre",{className:"mt-1 text-xs bg-gray-100 dark:bg-gray-800 p-2 rounded overflow-x-auto",children:"string"==typeof e.data?e.data:JSON.stringify(e.data,null,2)})]})]})]})},t))})}),(0,n.jsx)("div",{ref:l})]})]})}function C(){let[e,t]=(0,a.useState)("connections"),[r,s]=(0,a.useState)(!1),{connections:o,activeConnection:i,getActiveConnection:c}=f(),l=c(),d=o.filter(e=>e.status.connected).length,h=[{id:"connections",label:"Conexiones",count:o.length},{id:"tools",label:"Herramientas",count:(null==l?void 0:l.tools.length)||0},{id:"resources",label:"Recursos",count:(null==l?void 0:l.resources.length)||0},{id:"prompts",label:"Prompts",count:(null==l?void 0:l.prompts.length)||0},{id:"logs",label:"Logs",count:0}];return(0,n.jsxs)("div",{className:"space-y-6",children:[(0,n.jsx)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6",children:(0,n.jsxs)("div",{className:"flex items-center justify-between",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("h2",{className:"text-lg font-semibold text-gray-900 dark:text-white",children:"Estado del Cliente MCP"}),(0,n.jsxs)("p",{className:"text-sm text-gray-500 dark:text-gray-400 mt-1",children:[d," de ",o.length," conexiones activas",i&&(0,n.jsxs)("span",{className:"ml-2",children:["• Conexi\xf3n activa: ",(0,n.jsx)("span",{className:"font-medium",children:null==l?void 0:l.name})]})]})]}),(0,n.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,n.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,n.jsx)("div",{className:"w-3 h-3 rounded-full ".concat(d>0?"bg-green-500":"bg-gray-400")}),(0,n.jsx)("span",{className:"text-sm text-gray-600 dark:text-gray-300",children:d>0?"Conectado":"Desconectado"})]}),(0,n.jsx)("button",{onClick:()=>s(!0),className:"bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors",children:"Nueva Conexi\xf3n"})]})]})}),(0,n.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700",children:[(0,n.jsx)("div",{className:"border-b border-gray-200 dark:border-gray-700",children:(0,n.jsx)("nav",{className:"flex space-x-8 px-6","aria-label":"Tabs",children:h.map(r=>(0,n.jsxs)("button",{onClick:()=>t(r.id),className:"py-4 px-1 border-b-2 font-medium text-sm transition-colors ".concat(e===r.id?"border-blue-500 text-blue-600 dark:text-blue-400":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300"),children:[r.label,r.count>0&&(0,n.jsx)("span",{className:"ml-2 py-0.5 px-2 rounded-full text-xs ".concat(e===r.id?"bg-blue-100 text-blue-600 dark:bg-blue-900 dark:text-blue-300":"bg-gray-100 text-gray-600 dark:bg-gray-700 dark:text-gray-300"),children:r.count})]},r.id))})}),(0,n.jsxs)("div",{className:"p-6",children:["connections"===e&&(0,n.jsx)(j,{}),"tools"===e&&(0,n.jsx)(N,{}),"resources"===e&&(0,n.jsx)(w,{}),"prompts"===e&&(0,n.jsx)(S,{}),"logs"===e&&(0,n.jsx)(E,{})]})]}),r&&(0,n.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50",children:(0,n.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto",children:[(0,n.jsxs)("div",{className:"flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700",children:[(0,n.jsx)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white",children:"Nueva Conexi\xf3n MCP"}),(0,n.jsx)("button",{onClick:()=>s(!1),className:"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300",children:(0,n.jsx)("svg",{className:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,n.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})})]}),(0,n.jsx)("div",{className:"p-6",children:(0,n.jsx)(k,{onSuccess:()=>{s(!1),t("connections")},onCancel:()=>s(!1)})})]})})]})}function L(){return(0,n.jsx)(v,{children:(0,n.jsxs)("div",{className:"min-h-screen bg-gray-50 dark:bg-gray-900",children:[(0,n.jsx)("header",{className:"bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700",children:(0,n.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,n.jsxs)("div",{className:"flex justify-between items-center py-4",children:[(0,n.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,n.jsx)("div",{className:"w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center",children:(0,n.jsx)("span",{className:"text-white font-bold text-sm",children:"MCP"})}),(0,n.jsxs)("div",{children:[(0,n.jsx)("h1",{className:"text-xl font-semibold text-gray-900 dark:text-white",children:"MCP Web Client"}),(0,n.jsx)("p",{className:"text-sm text-gray-500 dark:text-gray-400",children:"Model Context Protocol Client"})]})]}),(0,n.jsx)("div",{className:"flex items-center space-x-2",children:(0,n.jsx)("span",{className:"text-sm text-gray-500 dark:text-gray-400",children:"v1.0.0"})})]})})}),(0,n.jsx)("main",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:(0,n.jsx)(C,{})})]})})}},7800:(e,t,r)=>{Promise.resolve().then(r.bind(r,5800))},9087:e=>{!function(){"use strict";var t={864:function(e){var t,r="object"==typeof Reflect?Reflect:null,s=r&&"function"==typeof r.apply?r.apply:function(e,t,r){return Function.prototype.apply.call(e,t,r)};t=r&&"function"==typeof r.ownKeys?r.ownKeys:Object.getOwnPropertySymbols?function(e){return Object.getOwnPropertyNames(e).concat(Object.getOwnPropertySymbols(e))}:function(e){return Object.getOwnPropertyNames(e)};var n=Number.isNaN||function(e){return e!=e};function a(){a.init.call(this)}e.exports=a,e.exports.once=function(e,t){return new Promise(function(r,s){var n,a,o;function i(r){e.removeListener(t,c),s(r)}function c(){"function"==typeof e.removeListener&&e.removeListener("error",i),r([].slice.call(arguments))}g(e,t,c,{once:!0}),"error"!==t&&(n=e,a=i,o={once:!0},"function"==typeof n.on&&g(n,"error",a,o))})},a.EventEmitter=a,a.prototype._events=void 0,a.prototype._eventsCount=0,a.prototype._maxListeners=void 0;var o=10;function i(e){if("function"!=typeof e)throw TypeError('The "listener" argument must be of type Function. Received type '+typeof e)}function c(e){return void 0===e._maxListeners?a.defaultMaxListeners:e._maxListeners}function l(e,t,r,s){if(i(r),void 0===(a=e._events)?(a=e._events=Object.create(null),e._eventsCount=0):(void 0!==a.newListener&&(e.emit("newListener",t,r.listener?r.listener:r),a=e._events),o=a[t]),void 0===o)o=a[t]=r,++e._eventsCount;else if("function"==typeof o?o=a[t]=s?[r,o]:[o,r]:s?o.unshift(r):o.push(r),(n=c(e))>0&&o.length>n&&!o.warned){o.warned=!0;var n,a,o,l=Error("Possible EventEmitter memory leak detected. "+o.length+" "+String(t)+" listeners added. Use emitter.setMaxListeners() to increase limit");l.name="MaxListenersExceededWarning",l.emitter=e,l.type=t,l.count=o.length,console&&console.warn&&console.warn(l)}return e}function d(){if(!this.fired)return(this.target.removeListener(this.type,this.wrapFn),this.fired=!0,0==arguments.length)?this.listener.call(this.target):this.listener.apply(this.target,arguments)}function h(e,t,r){var s={fired:!1,wrapFn:void 0,target:e,type:t,listener:r},n=d.bind(s);return n.listener=r,s.wrapFn=n,n}function m(e,t,r){var s=e._events;if(void 0===s)return[];var n=s[t];return void 0===n?[]:"function"==typeof n?r?[n.listener||n]:[n]:r?function(e){for(var t=Array(e.length),r=0;r<t.length;++r)t[r]=e[r].listener||e[r];return t}(n):x(n,n.length)}function u(e){var t=this._events;if(void 0!==t){var r=t[e];if("function"==typeof r)return 1;if(void 0!==r)return r.length}return 0}function x(e,t){for(var r=Array(t),s=0;s<t;++s)r[s]=e[s];return r}function g(e,t,r,s){if("function"==typeof e.on)s.once?e.once(t,r):e.on(t,r);else if("function"==typeof e.addEventListener)e.addEventListener(t,function n(a){s.once&&e.removeEventListener(t,n),r(a)});else throw TypeError('The "emitter" argument must be of type EventEmitter. Received type '+typeof e)}Object.defineProperty(a,"defaultMaxListeners",{enumerable:!0,get:function(){return o},set:function(e){if("number"!=typeof e||e<0||n(e))throw RangeError('The value of "defaultMaxListeners" is out of range. It must be a non-negative number. Received '+e+".");o=e}}),a.init=function(){(void 0===this._events||this._events===Object.getPrototypeOf(this)._events)&&(this._events=Object.create(null),this._eventsCount=0),this._maxListeners=this._maxListeners||void 0},a.prototype.setMaxListeners=function(e){if("number"!=typeof e||e<0||n(e))throw RangeError('The value of "n" is out of range. It must be a non-negative number. Received '+e+".");return this._maxListeners=e,this},a.prototype.getMaxListeners=function(){return c(this)},a.prototype.emit=function(e){for(var t=[],r=1;r<arguments.length;r++)t.push(arguments[r]);var n="error"===e,a=this._events;if(void 0!==a)n=n&&void 0===a.error;else if(!n)return!1;if(n){if(t.length>0&&(o=t[0]),o instanceof Error)throw o;var o,i=Error("Unhandled error."+(o?" ("+o.message+")":""));throw i.context=o,i}var c=a[e];if(void 0===c)return!1;if("function"==typeof c)s(c,this,t);else for(var l=c.length,d=x(c,l),r=0;r<l;++r)s(d[r],this,t);return!0},a.prototype.addListener=function(e,t){return l(this,e,t,!1)},a.prototype.on=a.prototype.addListener,a.prototype.prependListener=function(e,t){return l(this,e,t,!0)},a.prototype.once=function(e,t){return i(t),this.on(e,h(this,e,t)),this},a.prototype.prependOnceListener=function(e,t){return i(t),this.prependListener(e,h(this,e,t)),this},a.prototype.removeListener=function(e,t){var r,s,n,a,o;if(i(t),void 0===(s=this._events)||void 0===(r=s[e]))return this;if(r===t||r.listener===t)0==--this._eventsCount?this._events=Object.create(null):(delete s[e],s.removeListener&&this.emit("removeListener",e,r.listener||t));else if("function"!=typeof r){for(n=-1,a=r.length-1;a>=0;a--)if(r[a]===t||r[a].listener===t){o=r[a].listener,n=a;break}if(n<0)return this;0===n?r.shift():function(e,t){for(;t+1<e.length;t++)e[t]=e[t+1];e.pop()}(r,n),1===r.length&&(s[e]=r[0]),void 0!==s.removeListener&&this.emit("removeListener",e,o||t)}return this},a.prototype.off=a.prototype.removeListener,a.prototype.removeAllListeners=function(e){var t,r,s;if(void 0===(r=this._events))return this;if(void 0===r.removeListener)return 0==arguments.length?(this._events=Object.create(null),this._eventsCount=0):void 0!==r[e]&&(0==--this._eventsCount?this._events=Object.create(null):delete r[e]),this;if(0==arguments.length){var n,a=Object.keys(r);for(s=0;s<a.length;++s)"removeListener"!==(n=a[s])&&this.removeAllListeners(n);return this.removeAllListeners("removeListener"),this._events=Object.create(null),this._eventsCount=0,this}if("function"==typeof(t=r[e]))this.removeListener(e,t);else if(void 0!==t)for(s=t.length-1;s>=0;s--)this.removeListener(e,t[s]);return this},a.prototype.listeners=function(e){return m(this,e,!0)},a.prototype.rawListeners=function(e){return m(this,e,!1)},a.listenerCount=function(e,t){return"function"==typeof e.listenerCount?e.listenerCount(t):u.call(e,t)},a.prototype.listenerCount=u,a.prototype.eventNames=function(){return this._eventsCount>0?t(this._events):[]}}},r={};function s(e){var n=r[e];if(void 0!==n)return n.exports;var a=r[e]={exports:{}},o=!0;try{t[e](a,a.exports,s),o=!1}finally{o&&delete r[e]}return a.exports}s.ab="//",e.exports=s(864)}()}},e=>{var t=t=>e(e.s=t);e.O(0,[441,684,358],()=>t(7800)),_N_E=e.O()}]);