[{"D:\\serverN8N\\MCPClient\\mcp-web-client\\src\\app\\layout.tsx": "1", "D:\\serverN8N\\MCPClient\\mcp-web-client\\src\\app\\page.tsx": "2", "D:\\serverN8N\\MCPClient\\mcp-web-client\\src\\components\\ConnectionForm.tsx": "3", "D:\\serverN8N\\MCPClient\\mcp-web-client\\src\\components\\ConnectionList.tsx": "4", "D:\\serverN8N\\MCPClient\\mcp-web-client\\src\\components\\LogsPanel.tsx": "5", "D:\\serverN8N\\MCPClient\\mcp-web-client\\src\\components\\MCPClientProvider.tsx": "6", "D:\\serverN8N\\MCPClient\\mcp-web-client\\src\\components\\MCPDashboard.tsx": "7", "D:\\serverN8N\\MCPClient\\mcp-web-client\\src\\components\\PromptsPanel.tsx": "8", "D:\\serverN8N\\MCPClient\\mcp-web-client\\src\\components\\ResourcesPanel.tsx": "9", "D:\\serverN8N\\MCPClient\\mcp-web-client\\src\\components\\ToolsPanel.tsx": "10", "D:\\serverN8N\\MCPClient\\mcp-web-client\\src\\hooks\\useMCPClient.ts": "11", "D:\\serverN8N\\MCPClient\\mcp-web-client\\src\\lib\\mcp\\base-client.ts": "12", "D:\\serverN8N\\MCPClient\\mcp-web-client\\src\\lib\\mcp\\transports\\http-transport.ts": "13", "D:\\serverN8N\\MCPClient\\mcp-web-client\\src\\lib\\mcp\\transports\\index.ts": "14", "D:\\serverN8N\\MCPClient\\mcp-web-client\\src\\lib\\mcp\\transports\\sse-transport.ts": "15", "D:\\serverN8N\\MCPClient\\mcp-web-client\\src\\lib\\mcp\\transports\\stdio-transport.ts": "16", "D:\\serverN8N\\MCPClient\\mcp-web-client\\src\\types\\mcp.ts": "17"}, {"size": 689, "mtime": 1751214623660, "results": "18", "hashOfConfig": "19"}, {"size": 1559, "mtime": 1751215134950, "results": "20", "hashOfConfig": "19"}, {"size": 9816, "mtime": 1751215266712, "results": "21", "hashOfConfig": "19"}, {"size": 10373, "mtime": 1751215225527, "results": "22", "hashOfConfig": "19"}, {"size": 10657, "mtime": 1751215439669, "results": "23", "hashOfConfig": "19"}, {"size": 1922, "mtime": 1751215155746, "results": "24", "hashOfConfig": "19"}, {"size": 5931, "mtime": 1751215181603, "results": "25", "hashOfConfig": "19"}, {"size": 10052, "mtime": 1751215393493, "results": "26", "hashOfConfig": "19"}, {"size": 9049, "mtime": 1751215353943, "results": "27", "hashOfConfig": "19"}, {"size": 12860, "mtime": 1751215317776, "results": "28", "hashOfConfig": "19"}, {"size": 9885, "mtime": 1751215060948, "results": "29", "hashOfConfig": "19"}, {"size": 8938, "mtime": 1751214842266, "results": "30", "hashOfConfig": "19"}, {"size": 9814, "mtime": 1751214984508, "results": "31", "hashOfConfig": "19"}, {"size": 2760, "mtime": 1751215758717, "results": "32", "hashOfConfig": "19"}, {"size": 6311, "mtime": 1751214928512, "results": "33", "hashOfConfig": "19"}, {"size": 7146, "mtime": 1751215650179, "results": "34", "hashOfConfig": "19"}, {"size": 4484, "mtime": 1751214797964, "results": "35", "hashOfConfig": "19"}, {"filePath": "36", "messages": "37", "suppressedMessages": "38", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1rvsde4", {"filePath": "39", "messages": "40", "suppressedMessages": "41", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "42", "messages": "43", "suppressedMessages": "44", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "45", "messages": "46", "suppressedMessages": "47", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "48", "messages": "49", "suppressedMessages": "50", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "51", "messages": "52", "suppressedMessages": "53", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "54", "messages": "55", "suppressedMessages": "56", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "57", "messages": "58", "suppressedMessages": "59", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "60", "messages": "61", "suppressedMessages": "62", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "63", "messages": "64", "suppressedMessages": "65", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "66", "messages": "67", "suppressedMessages": "68", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "69", "messages": "70", "suppressedMessages": "71", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "72", "messages": "73", "suppressedMessages": "74", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "75", "messages": "76", "suppressedMessages": "77", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "78", "messages": "79", "suppressedMessages": "80", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "81", "messages": "82", "suppressedMessages": "83", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "84", "messages": "85", "suppressedMessages": "86", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 13, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "D:\\serverN8N\\MCPClient\\mcp-web-client\\src\\app\\layout.tsx", [], [], "D:\\serverN8N\\MCPClient\\mcp-web-client\\src\\app\\page.tsx", [], [], "D:\\serverN8N\\MCPClient\\mcp-web-client\\src\\components\\ConnectionForm.tsx", ["87"], [], "D:\\serverN8N\\MCPClient\\mcp-web-client\\src\\components\\ConnectionList.tsx", [], [], "D:\\serverN8N\\MCPClient\\mcp-web-client\\src\\components\\LogsPanel.tsx", [], [], "D:\\serverN8N\\MCPClient\\mcp-web-client\\src\\components\\MCPClientProvider.tsx", [], [], "D:\\serverN8N\\MCPClient\\mcp-web-client\\src\\components\\MCPDashboard.tsx", [], [], "D:\\serverN8N\\MCPClient\\mcp-web-client\\src\\components\\PromptsPanel.tsx", ["88"], [], "D:\\serverN8N\\MCPClient\\mcp-web-client\\src\\components\\ResourcesPanel.tsx", ["89"], [], "D:\\serverN8N\\MCPClient\\mcp-web-client\\src\\components\\ToolsPanel.tsx", ["90", "91", "92", "93", "94", "95"], [], "D:\\serverN8N\\MCPClient\\mcp-web-client\\src\\hooks\\useMCPClient.ts", ["96", "97", "98"], [], "D:\\serverN8N\\MCPClient\\mcp-web-client\\src\\lib\\mcp\\base-client.ts", ["99", "100", "101", "102", "103", "104", "105", "106"], [], "D:\\serverN8N\\MCPClient\\mcp-web-client\\src\\lib\\mcp\\transports\\http-transport.ts", ["107", "108", "109", "110", "111"], [], "D:\\serverN8N\\MCPClient\\mcp-web-client\\src\\lib\\mcp\\transports\\index.ts", ["112", "113", "114"], [], "D:\\serverN8N\\MCPClient\\mcp-web-client\\src\\lib\\mcp\\transports\\sse-transport.ts", ["115", "116", "117", "118", "119"], [], "D:\\serverN8N\\MCPClient\\mcp-web-client\\src\\lib\\mcp\\transports\\stdio-transport.ts", ["120"], [], "D:\\serverN8N\\MCPClient\\mcp-web-client\\src\\types\\mcp.ts", ["121", "122", "123", "124", "125", "126", "127", "128", "129", "130", "131", "132", "133"], [], {"ruleId": "134", "severity": 1, "message": "135", "line": 77, "column": 31, "nodeType": "136", "messageId": "137", "endLine": 77, "endColumn": 34, "suggestions": "138"}, {"ruleId": "139", "severity": 1, "message": "140", "line": 210, "column": 31, "nodeType": "141", "endLine": 214, "endColumn": 33}, {"ruleId": "139", "severity": 1, "message": "140", "line": 173, "column": 29, "nodeType": "141", "endLine": 177, "endColumn": 31}, {"ruleId": "134", "severity": 1, "message": "135", "line": 10, "column": 59, "nodeType": "136", "messageId": "137", "endLine": 10, "endColumn": 62, "suggestions": "142"}, {"ruleId": "134", "severity": 1, "message": "135", "line": 24, "column": 39, "nodeType": "136", "messageId": "137", "endLine": 24, "endColumn": 42, "suggestions": "143"}, {"ruleId": "134", "severity": 1, "message": "135", "line": 64, "column": 52, "nodeType": "136", "messageId": "137", "endLine": 64, "endColumn": 55, "suggestions": "144"}, {"ruleId": "134", "severity": 1, "message": "135", "line": 235, "column": 35, "nodeType": "136", "messageId": "137", "endLine": 235, "endColumn": 38, "suggestions": "145"}, {"ruleId": "134", "severity": 1, "message": "135", "line": 237, "column": 39, "nodeType": "136", "messageId": "137", "endLine": 237, "endColumn": 42, "suggestions": "146"}, {"ruleId": "139", "severity": 1, "message": "140", "line": 276, "column": 27, "nodeType": "141", "endLine": 280, "endColumn": 29}, {"ruleId": "134", "severity": 1, "message": "135", "line": 31, "column": 104, "nodeType": "136", "messageId": "137", "endLine": 31, "endColumn": 107, "suggestions": "147"}, {"ruleId": "134", "severity": 1, "message": "135", "line": 104, "column": 41, "nodeType": "136", "messageId": "137", "endLine": 104, "endColumn": 44, "suggestions": "148"}, {"ruleId": "149", "severity": 1, "message": "150", "line": 277, "column": 18, "nodeType": "151", "endLine": 277, "endColumn": 25}, {"ruleId": "152", "severity": 1, "message": "153", "line": 6, "column": 3, "nodeType": null, "messageId": "154", "endLine": 6, "endColumn": 14}, {"ruleId": "152", "severity": 1, "message": "155", "line": 23, "column": 3, "nodeType": null, "messageId": "154", "endLine": 23, "endColumn": 18}, {"ruleId": "152", "severity": 1, "message": "156", "line": 24, "column": 3, "nodeType": null, "messageId": "154", "endLine": 24, "endColumn": 18}, {"ruleId": "134", "severity": 1, "message": "135", "line": 67, "column": 22, "nodeType": "136", "messageId": "137", "endLine": 67, "endColumn": 25, "suggestions": "157"}, {"ruleId": "134", "severity": 1, "message": "135", "line": 181, "column": 54, "nodeType": "136", "messageId": "137", "endLine": 181, "endColumn": 57, "suggestions": "158"}, {"ruleId": "134", "severity": 1, "message": "135", "line": 181, "column": 68, "nodeType": "136", "messageId": "137", "endLine": 181, "endColumn": 71, "suggestions": "159"}, {"ruleId": "134", "severity": 1, "message": "135", "line": 229, "column": 59, "nodeType": "136", "messageId": "137", "endLine": 229, "endColumn": 62, "suggestions": "160"}, {"ruleId": "152", "severity": 1, "message": "161", "line": 274, "column": 17, "nodeType": null, "messageId": "154", "endLine": 274, "endColumn": 19}, {"ruleId": "134", "severity": 1, "message": "135", "line": 99, "column": 59, "nodeType": "136", "messageId": "137", "endLine": 99, "endColumn": 62, "suggestions": "162"}, {"ruleId": "152", "severity": 1, "message": "163", "line": 105, "column": 18, "nodeType": null, "messageId": "154", "endLine": 105, "endColumn": 23}, {"ruleId": "134", "severity": 1, "message": "135", "line": 111, "column": 58, "nodeType": "136", "messageId": "137", "endLine": 111, "endColumn": 61, "suggestions": "164"}, {"ruleId": "152", "severity": 1, "message": "163", "line": 120, "column": 33, "nodeType": null, "messageId": "154", "endLine": 120, "endColumn": 38}, {"ruleId": "152", "severity": 1, "message": "163", "line": 328, "column": 12, "nodeType": null, "messageId": "154", "endLine": 328, "endColumn": 17}, {"ruleId": "134", "severity": 1, "message": "135", "line": 40, "column": 43, "nodeType": "136", "messageId": "137", "endLine": 40, "endColumn": 46, "suggestions": "165"}, {"ruleId": "134", "severity": 1, "message": "135", "line": 42, "column": 41, "nodeType": "136", "messageId": "137", "endLine": 42, "endColumn": 44, "suggestions": "166"}, {"ruleId": "134", "severity": 1, "message": "135", "line": 45, "column": 42, "nodeType": "136", "messageId": "137", "endLine": 45, "endColumn": 45, "suggestions": "167"}, {"ruleId": "134", "severity": 1, "message": "135", "line": 74, "column": 61, "nodeType": "136", "messageId": "137", "endLine": 74, "endColumn": 64, "suggestions": "168"}, {"ruleId": "152", "severity": 1, "message": "163", "line": 84, "column": 18, "nodeType": null, "messageId": "154", "endLine": 84, "endColumn": 23}, {"ruleId": "134", "severity": 1, "message": "135", "line": 90, "column": 60, "nodeType": "136", "messageId": "137", "endLine": 90, "endColumn": 63, "suggestions": "169"}, {"ruleId": "152", "severity": 1, "message": "163", "line": 105, "column": 35, "nodeType": null, "messageId": "154", "endLine": 105, "endColumn": 40}, {"ruleId": "152", "severity": 1, "message": "163", "line": 207, "column": 12, "nodeType": null, "messageId": "154", "endLine": 207, "endColumn": 17}, {"ruleId": "134", "severity": 1, "message": "135", "line": 13, "column": 20, "nodeType": "136", "messageId": "137", "endLine": 13, "endColumn": 23, "suggestions": "170"}, {"ruleId": "134", "severity": 1, "message": "135", "line": 7, "column": 12, "nodeType": "136", "messageId": "137", "endLine": 7, "endColumn": 15, "suggestions": "171"}, {"ruleId": "134", "severity": 1, "message": "135", "line": 8, "column": 12, "nodeType": "136", "messageId": "137", "endLine": 8, "endColumn": 15, "suggestions": "172"}, {"ruleId": "134", "severity": 1, "message": "135", "line": 15, "column": 10, "nodeType": "136", "messageId": "137", "endLine": 15, "endColumn": 13, "suggestions": "173"}, {"ruleId": "134", "severity": 1, "message": "135", "line": 20, "column": 12, "nodeType": "136", "messageId": "137", "endLine": 20, "endColumn": 15, "suggestions": "174"}, {"ruleId": "134", "severity": 1, "message": "135", "line": 24, "column": 12, "nodeType": "136", "messageId": "137", "endLine": 24, "endColumn": 15, "suggestions": "175"}, {"ruleId": "134", "severity": 1, "message": "135", "line": 30, "column": 12, "nodeType": "136", "messageId": "137", "endLine": 30, "endColumn": 15, "suggestions": "176"}, {"ruleId": "177", "severity": 1, "message": "178", "line": 63, "column": 14, "nodeType": "179", "messageId": "180", "endLine": 63, "endColumn": 16, "suggestions": "181"}, {"ruleId": "177", "severity": 1, "message": "178", "line": 64, "column": 17, "nodeType": "179", "messageId": "180", "endLine": 64, "endColumn": 19, "suggestions": "182"}, {"ruleId": "177", "severity": 1, "message": "178", "line": 68, "column": 13, "nodeType": "179", "messageId": "180", "endLine": 68, "endColumn": 15, "suggestions": "183"}, {"ruleId": "134", "severity": 1, "message": "135", "line": 90, "column": 31, "nodeType": "136", "messageId": "137", "endLine": 90, "endColumn": 34, "suggestions": "184"}, {"ruleId": "134", "severity": 1, "message": "135", "line": 96, "column": 30, "nodeType": "136", "messageId": "137", "endLine": 96, "endColumn": 33, "suggestions": "185"}, {"ruleId": "134", "severity": 1, "message": "135", "line": 172, "column": 9, "nodeType": "136", "messageId": "137", "endLine": 172, "endColumn": 12, "suggestions": "186"}, {"ruleId": "134", "severity": 1, "message": "135", "line": 217, "column": 10, "nodeType": "136", "messageId": "137", "endLine": 217, "endColumn": 13, "suggestions": "187"}, "@typescript-eslint/no-explicit-any", "Unexpected any. Specify a different type.", "TSAnyKeyword", "unexpectedAny", ["188", "189"], "@next/next/no-img-element", "Using `<img>` could result in slower LCP and higher bandwidth. Consider using `<Image />` from `next/image` or a custom image loader to automatically optimize images. This may incur additional usage or cost from your provider. See: https://nextjs.org/docs/messages/no-img-element", "JSXOpeningElement", ["190", "191"], ["192", "193"], ["194", "195"], ["196", "197"], ["198", "199"], ["200", "201"], ["202", "203"], "react-hooks/exhaustive-deps", "The ref value 'clientsRef.current' will likely have changed by the time this effect cleanup function runs. If this ref points to a node rendered by React, copy 'clientsRef.current' to a variable inside the effect, and use that variable in the cleanup function.", "Identifier", "@typescript-eslint/no-unused-vars", "'MCPResponse' is defined but never used.", "unusedVar", "'TransportConfig' is defined but never used.", "'MCPClientEvents' is defined but never used.", ["204", "205"], ["206", "207"], ["208", "209"], ["210", "211"], "'id' is assigned a value but never used.", ["212", "213"], "'error' is defined but never used.", ["214", "215"], ["216", "217"], ["218", "219"], ["220", "221"], ["222", "223"], ["224", "225"], ["226", "227"], ["228", "229"], ["230", "231"], ["232", "233"], ["234", "235"], ["236", "237"], ["238", "239"], "@typescript-eslint/no-empty-object-type", "The `{}` (\"empty object\") type allows any non-nullish value, including literals like `0` and `\"\"`.\n- If that's what you want, disable this lint rule with an inline comment or configure the 'allowObjectTypes' rule option.\n- If you want a type meaning \"any object\", you probably want `object` instead.\n- If you want a type meaning \"any value\", you probably want `unknown` instead.", "TSTypeLiteral", "noEmptyObject", ["240", "241"], ["242", "243"], ["244", "245"], ["246", "247"], ["248", "249"], ["250", "251"], ["252", "253"], {"messageId": "254", "fix": "255", "desc": "256"}, {"messageId": "257", "fix": "258", "desc": "259"}, {"messageId": "254", "fix": "260", "desc": "256"}, {"messageId": "257", "fix": "261", "desc": "259"}, {"messageId": "254", "fix": "262", "desc": "256"}, {"messageId": "257", "fix": "263", "desc": "259"}, {"messageId": "254", "fix": "264", "desc": "256"}, {"messageId": "257", "fix": "265", "desc": "259"}, {"messageId": "254", "fix": "266", "desc": "256"}, {"messageId": "257", "fix": "267", "desc": "259"}, {"messageId": "254", "fix": "268", "desc": "256"}, {"messageId": "257", "fix": "269", "desc": "259"}, {"messageId": "254", "fix": "270", "desc": "256"}, {"messageId": "257", "fix": "271", "desc": "259"}, {"messageId": "254", "fix": "272", "desc": "256"}, {"messageId": "257", "fix": "273", "desc": "259"}, {"messageId": "254", "fix": "274", "desc": "256"}, {"messageId": "257", "fix": "275", "desc": "259"}, {"messageId": "254", "fix": "276", "desc": "256"}, {"messageId": "257", "fix": "277", "desc": "259"}, {"messageId": "254", "fix": "278", "desc": "256"}, {"messageId": "257", "fix": "279", "desc": "259"}, {"messageId": "254", "fix": "280", "desc": "256"}, {"messageId": "257", "fix": "281", "desc": "259"}, {"messageId": "254", "fix": "282", "desc": "256"}, {"messageId": "257", "fix": "283", "desc": "259"}, {"messageId": "254", "fix": "284", "desc": "256"}, {"messageId": "257", "fix": "285", "desc": "259"}, {"messageId": "254", "fix": "286", "desc": "256"}, {"messageId": "257", "fix": "287", "desc": "259"}, {"messageId": "254", "fix": "288", "desc": "256"}, {"messageId": "257", "fix": "289", "desc": "259"}, {"messageId": "254", "fix": "290", "desc": "256"}, {"messageId": "257", "fix": "291", "desc": "259"}, {"messageId": "254", "fix": "292", "desc": "256"}, {"messageId": "257", "fix": "293", "desc": "259"}, {"messageId": "254", "fix": "294", "desc": "256"}, {"messageId": "257", "fix": "295", "desc": "259"}, {"messageId": "254", "fix": "296", "desc": "256"}, {"messageId": "257", "fix": "297", "desc": "259"}, {"messageId": "254", "fix": "298", "desc": "256"}, {"messageId": "257", "fix": "299", "desc": "259"}, {"messageId": "254", "fix": "300", "desc": "256"}, {"messageId": "257", "fix": "301", "desc": "259"}, {"messageId": "254", "fix": "302", "desc": "256"}, {"messageId": "257", "fix": "303", "desc": "259"}, {"messageId": "254", "fix": "304", "desc": "256"}, {"messageId": "257", "fix": "305", "desc": "259"}, {"messageId": "254", "fix": "306", "desc": "256"}, {"messageId": "257", "fix": "307", "desc": "259"}, {"messageId": "254", "fix": "308", "desc": "256"}, {"messageId": "257", "fix": "309", "desc": "259"}, {"messageId": "310", "data": "311", "fix": "312", "desc": "313"}, {"messageId": "310", "data": "314", "fix": "315", "desc": "316"}, {"messageId": "310", "data": "317", "fix": "318", "desc": "313"}, {"messageId": "310", "data": "319", "fix": "320", "desc": "316"}, {"messageId": "310", "data": "321", "fix": "322", "desc": "313"}, {"messageId": "310", "data": "323", "fix": "324", "desc": "316"}, {"messageId": "254", "fix": "325", "desc": "256"}, {"messageId": "257", "fix": "326", "desc": "259"}, {"messageId": "254", "fix": "327", "desc": "256"}, {"messageId": "257", "fix": "328", "desc": "259"}, {"messageId": "254", "fix": "329", "desc": "256"}, {"messageId": "257", "fix": "330", "desc": "259"}, {"messageId": "254", "fix": "331", "desc": "256"}, {"messageId": "257", "fix": "332", "desc": "259"}, "suggestUnknown", {"range": "333", "text": "334"}, "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct.", "suggestNever", {"range": "335", "text": "336"}, "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of.", {"range": "337", "text": "334"}, {"range": "338", "text": "336"}, {"range": "339", "text": "334"}, {"range": "340", "text": "336"}, {"range": "341", "text": "334"}, {"range": "342", "text": "336"}, {"range": "343", "text": "334"}, {"range": "344", "text": "336"}, {"range": "345", "text": "334"}, {"range": "346", "text": "336"}, {"range": "347", "text": "334"}, {"range": "348", "text": "336"}, {"range": "349", "text": "334"}, {"range": "350", "text": "336"}, {"range": "351", "text": "334"}, {"range": "352", "text": "336"}, {"range": "353", "text": "334"}, {"range": "354", "text": "336"}, {"range": "355", "text": "334"}, {"range": "356", "text": "336"}, {"range": "357", "text": "334"}, {"range": "358", "text": "336"}, {"range": "359", "text": "334"}, {"range": "360", "text": "336"}, {"range": "361", "text": "334"}, {"range": "362", "text": "336"}, {"range": "363", "text": "334"}, {"range": "364", "text": "336"}, {"range": "365", "text": "334"}, {"range": "366", "text": "336"}, {"range": "367", "text": "334"}, {"range": "368", "text": "336"}, {"range": "369", "text": "334"}, {"range": "370", "text": "336"}, {"range": "371", "text": "334"}, {"range": "372", "text": "336"}, {"range": "373", "text": "334"}, {"range": "374", "text": "336"}, {"range": "375", "text": "334"}, {"range": "376", "text": "336"}, {"range": "377", "text": "334"}, {"range": "378", "text": "336"}, {"range": "379", "text": "334"}, {"range": "380", "text": "336"}, {"range": "381", "text": "334"}, {"range": "382", "text": "336"}, {"range": "383", "text": "334"}, {"range": "384", "text": "336"}, {"range": "385", "text": "334"}, {"range": "386", "text": "336"}, "replaceEmptyObjectType", {"replacement": "387"}, {"range": "388", "text": "387"}, "Replace `{}` with `object`.", {"replacement": "334"}, {"range": "389", "text": "334"}, "Replace `{}` with `unknown`.", {"replacement": "387"}, {"range": "390", "text": "387"}, {"replacement": "334"}, {"range": "391", "text": "334"}, {"replacement": "387"}, {"range": "392", "text": "387"}, {"replacement": "334"}, {"range": "393", "text": "334"}, {"range": "394", "text": "334"}, {"range": "395", "text": "336"}, {"range": "396", "text": "334"}, {"range": "397", "text": "336"}, {"range": "398", "text": "334"}, {"range": "399", "text": "336"}, {"range": "400", "text": "334"}, {"range": "401", "text": "336"}, [2254, 2257], "unknown", [2254, 2257], "never", [412, 415], [412, 415], [909, 912], [909, 912], [2066, 2069], [2066, 2069], [9745, 9748], [9745, 9748], [9889, 9892], [9889, 9892], [799, 802], [799, 802], [2782, 2785], [2782, 2785], [1463, 1466], [1463, 1466], [4713, 4716], [4713, 4716], [4727, 4730], [4727, 4730], [6082, 6085], [6082, 6085], [2858, 2861], [2858, 2861], [3232, 3235], [3232, 3235], [1041, 1044], [1041, 1044], [1103, 1106], [1103, 1106], [1195, 1198], [1195, 1198], [2091, 2094], [2091, 2094], [2541, 2544], [2541, 2544], [379, 382], [379, 382], [157, 160], [157, 160], [173, 176], [173, 176], [273, 276], [273, 276], [359, 362], [359, 362], [428, 431], [428, 431], [539, 542], [539, 542], "object", [1125, 1127], [1125, 1127], [1145, 1147], [1145, 1147], [1202, 1204], [1202, 1204], [1579, 1582], [1579, 1582], [1691, 1694], [1691, 1694], [3093, 3096], [3093, 3096], [3997, 4000], [3997, 4000]]