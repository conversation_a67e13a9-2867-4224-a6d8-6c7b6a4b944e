(()=>{var e={};e.id=974,e.ids=[974],e.modules={440:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});var n=r(1658);let s=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,n.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},554:(e,t)=>{"use strict";function r(e){return e.endsWith("/route")}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isAppRouteRoute",{enumerable:!0,get:function(){return r}})},660:(e,t)=>{"use strict";function r(e){let t=5381;for(let r=0;r<e.length;r++)t=(t<<5)+t+e.charCodeAt(r)|0;return t>>>0}function n(e){return r(e).toString(36).slice(0,5)}Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{djb2Hash:function(){return r},hexHash:function(){return n}})},786:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>i.a,__next_app__:()=>u,pages:()=>d,routeModule:()=>m,tree:()=>c});var n=r(5239),s=r(8088),a=r(8170),i=r.n(a),o=r(893),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);r.d(t,l);let c={children:["",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,1204)),"D:\\serverN8N\\MCPClient\\mcp-web-client\\src\\app\\page.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,4431)),"D:\\serverN8N\\MCPClient\\mcp-web-client\\src\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,7398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,9999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,5284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,440))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,d=["D:\\serverN8N\\MCPClient\\mcp-web-client\\src\\app\\page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},m=new n.AppPageRouteModule({definition:{kind:s.RouteKind.APP_PAGE,page:"/page",pathname:"/",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1135:()=>{},1204:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>n});let n=(0,r(2907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"D:\\\\serverN8N\\\\MCPClient\\\\mcp-web-client\\\\src\\\\app\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"D:\\serverN8N\\MCPClient\\mcp-web-client\\src\\app\\page.tsx","default")},1354:()=>{},1437:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{INTERCEPTION_ROUTE_MARKERS:function(){return s},extractInterceptionRouteInformation:function(){return i},isInterceptionRouteAppPath:function(){return a}});let n=r(4722),s=["(..)(..)","(.)","(..)","(...)"];function a(e){return void 0!==e.split("/").find(e=>s.find(t=>e.startsWith(t)))}function i(e){let t,r,a;for(let n of e.split("/"))if(r=s.find(e=>n.startsWith(e))){[t,a]=e.split(r,2);break}if(!t||!r||!a)throw Object.defineProperty(Error("Invalid interception route: "+e+". Must be in the format /<intercepting route>/(..|...|..)(..)/<intercepted route>"),"__NEXT_ERROR_CODE",{value:"E269",enumerable:!1,configurable:!0});switch(t=(0,n.normalizeAppPath)(t),r){case"(.)":a="/"===t?"/"+a:t+"/"+a;break;case"(..)":if("/"===t)throw Object.defineProperty(Error("Invalid interception route: "+e+". Cannot use (..) marker at the root level, use (.) instead."),"__NEXT_ERROR_CODE",{value:"E207",enumerable:!1,configurable:!0});a=t.split("/").slice(0,-1).concat(a).join("/");break;case"(...)":a="/"+a;break;case"(..)(..)":let i=t.split("/");if(i.length<=2)throw Object.defineProperty(Error("Invalid interception route: "+e+". Cannot use (..)(..) marker at the root level or one level up."),"__NEXT_ERROR_CODE",{value:"E486",enumerable:!1,configurable:!0});a=i.slice(0,-2).concat(a).join("/");break;default:throw Object.defineProperty(Error("Invariant: unexpected marker"),"__NEXT_ERROR_CODE",{value:"E112",enumerable:!1,configurable:!0})}return{interceptingRoute:t,interceptedRoute:a}}},1658:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{fillMetadataSegment:function(){return m},normalizeMetadataPageToRoute:function(){return p},normalizeMetadataRoute:function(){return h}});let n=r(8304),s=function(e){return e&&e.__esModule?e:{default:e}}(r(8671)),a=r(6341),i=r(4396),o=r(660),l=r(4722),c=r(2958),d=r(5499);function u(e){let t=s.default.dirname(e);if(e.endsWith("/sitemap"))return"";let r="";return t.split("/").some(e=>(0,d.isGroupSegment)(e)||(0,d.isParallelRouteSegment)(e))&&(r=(0,o.djb2Hash)(t).toString(36).slice(0,6)),r}function m(e,t,r){let n=(0,l.normalizeAppPath)(e),o=(0,i.getNamedRouteRegex)(n,{prefixRouteKeys:!1}),d=(0,a.interpolateDynamicPath)(n,t,o),{name:m,ext:h}=s.default.parse(r),p=u(s.default.posix.join(e,m)),g=p?`-${p}`:"";return(0,c.normalizePathSep)(s.default.join(d,`${m}${g}${h}`))}function h(e){if(!(0,n.isMetadataPage)(e))return e;let t=e,r="";if("/robots"===e?t+=".txt":"/manifest"===e?t+=".webmanifest":r=u(e),!t.endsWith("/route")){let{dir:e,name:n,ext:a}=s.default.parse(t);t=s.default.posix.join(e,`${n}${r?`-${r}`:""}${a}`,"route")}return t}function p(e,t){let r=e.endsWith("/route"),n=r?e.slice(0,-6):e,s=n.endsWith("/sitemap")?".xml":"";return(t?`${n}/[__metadata_id__]`:`${n}${s}`)+(r?"/route":"")}},2013:(e,t,r)=>{Promise.resolve().then(r.bind(r,2714))},2437:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getPathMatch",{enumerable:!0,get:function(){return s}});let n=r(5362);function s(e,t){let r=[],s=(0,n.pathToRegexp)(e,r,{delimiter:"/",sensitive:"boolean"==typeof(null==t?void 0:t.sensitive)&&t.sensitive,strict:null==t?void 0:t.strict}),a=(0,n.regexpToFunction)((null==t?void 0:t.regexModifier)?new RegExp(t.regexModifier(s.source),s.flags):s,r);return(e,n)=>{if("string"!=typeof e)return!1;let s=a(e);if(!s)return!1;if(null==t?void 0:t.removeUnnamedParams)for(let e of r)"number"==typeof e.name&&delete s.params[e.name];return{...n,...s.params}}}},2714:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>P});var n=r(687),s=r(3210);let a=require("events"),i=require("crypto"),o={randomUUID:i.randomUUID},l=new Uint8Array(256),c=l.length,d=[];for(let e=0;e<256;++e)d.push((e+256).toString(16).slice(1));let u=function(e,t,r){if(o.randomUUID&&!t&&!e)return o.randomUUID();let n=(e=e||{}).random??e.rng?.()??(c>l.length-16&&((0,i.randomFillSync)(l),c=0),l.slice(c,c+=16));if(n.length<16)throw Error("Random bytes length must be >= 16");if(n[6]=15&n[6]|64,n[8]=63&n[8]|128,t){if((r=r||0)<0||r+16>t.length)throw RangeError(`UUID byte range ${r}:${r+15} is out of buffer bounds`);for(let e=0;e<16;++e)t[r+e]=n[e];return t}return function(e,t=0){return(d[e[t+0]]+d[e[t+1]]+d[e[t+2]]+d[e[t+3]]+"-"+d[e[t+4]]+d[e[t+5]]+"-"+d[e[t+6]]+d[e[t+7]]+"-"+d[e[t+8]]+d[e[t+9]]+"-"+d[e[t+10]]+d[e[t+11]]+d[e[t+12]]+d[e[t+13]]+d[e[t+14]]+d[e[t+15]]).toLowerCase()}(n)};class m extends a.EventEmitter{isConnected(){return this.connected}isConnecting(){return this.connecting}handleMessage(e){this.emit("message",e)}handleError(e){this.emit("error",e)}handleConnect(){this.connected=!0,this.connecting=!1,this.emit("connect")}handleDisconnect(){this.connected=!1,this.connecting=!1,this.emit("disconnect")}constructor(...e){super(...e),this.connected=!1,this.connecting=!1}}class h extends a.EventEmitter{constructor(e,t={}){super(),this.transport=null,this.pendingRequests=new Map,this.requestTimeout=3e4,this.serverInfo=null,this.tools=[],this.resources=[],this.prompts=[],this.initialized=!1,this.clientInfo=e,this.capabilities=t}async connect(e){this.transport&&await this.disconnect(),this.transport=e,this.setupTransportListeners();try{await this.transport.connect(),await this.initialize()}catch(e){throw this.transport=null,e}}async disconnect(){this.transport&&(this.removeTransportListeners(),await this.transport.disconnect(),this.transport=null),this.initialized=!1,this.serverInfo=null,this.tools=[],this.resources=[],this.prompts=[],this.clearPendingRequests()}setupTransportListeners(){this.transport&&(this.transport.on("message",this.handleMessage.bind(this)),this.transport.on("error",this.handleTransportError.bind(this)),this.transport.on("disconnect",this.handleTransportDisconnect.bind(this)))}removeTransportListeners(){this.transport&&(this.transport.removeAllListeners("message"),this.transport.removeAllListeners("error"),this.transport.removeAllListeners("disconnect"))}handleMessage(e){if(void 0!==e.id){let t=this.pendingRequests.get(e.id);t&&(clearTimeout(t.timeout),this.pendingRequests.delete(e.id),e.error?t.reject(Error(`MCP Error ${e.error.code}: ${e.error.message}`)):t.resolve(e.result))}else e.method&&this.handleNotification(e);this.emit("message",e)}handleNotification(e){switch(e.method){case"notifications/message":this.emit("log",e.params);break;case"notifications/tools/list_changed":this.refreshTools();break;case"notifications/resources/list_changed":this.refreshResources();break;case"notifications/prompts/list_changed":this.refreshPrompts()}}handleTransportError(e){this.emit("error",e)}handleTransportDisconnect(){this.initialized=!1,this.emit("disconnect")}async sendRequest(e,t){if(!this.transport||!this.transport.isConnected())throw Error("No hay conexi\xf3n activa");let r=u(),n={jsonrpc:"2.0",id:r,method:e,params:t};return new Promise((t,s)=>{let a=setTimeout(()=>{this.pendingRequests.delete(r),s(Error(`Timeout: No se recibi\xf3 respuesta para ${e}`))},this.requestTimeout);this.pendingRequests.set(r,{resolve:t,reject:s,timeout:a}),this.transport.send(n).catch(s)})}async initialize(){let e={protocolVersion:"2024-11-05",capabilities:this.capabilities,clientInfo:this.clientInfo},t=await this.sendRequest("initialize",e);this.serverInfo=t.serverInfo,await this.sendNotification("notifications/initialized"),await Promise.all([this.refreshTools(),this.refreshResources(),this.refreshPrompts()]),this.initialized=!0,this.emit("initialized",t)}async sendNotification(e,t){if(!this.transport||!this.transport.isConnected())throw Error("No hay conexi\xf3n activa");await this.transport.send({jsonrpc:"2.0",method:e,params:t})}async refreshTools(){try{let e=await this.sendRequest("tools/list");this.tools=e.tools||[],this.emit("toolsChanged",this.tools)}catch(e){console.error("Error refreshing tools:",e)}}async refreshResources(){try{let e=await this.sendRequest("resources/list");this.resources=e.resources||[],this.emit("resourcesChanged",this.resources)}catch(e){console.error("Error refreshing resources:",e)}}async refreshPrompts(){try{let e=await this.sendRequest("prompts/list");this.prompts=e.prompts||[],this.emit("promptsChanged",this.prompts)}catch(e){console.error("Error refreshing prompts:",e)}}clearPendingRequests(){for(let[e,t]of this.pendingRequests)clearTimeout(t.timeout),t.reject(Error("Conexi\xf3n cerrada"));this.pendingRequests.clear()}isInitialized(){return this.initialized}isConnected(){return this.transport?.isConnected()||!1}getServerInfo(){return this.serverInfo}getTools(){return[...this.tools]}getResources(){return[...this.resources]}getPrompts(){return[...this.prompts]}async callTool(e){if(!this.initialized)throw Error("Cliente no inicializado");let t=await this.sendRequest("tools/call",e);return this.emit("toolResult",e.name,t),t}async readResource(e){if(!this.initialized)throw Error("Cliente no inicializado");return await this.sendRequest("resources/read",e)}async getPrompt(e){if(!this.initialized)throw Error("Cliente no inicializado");return await this.sendRequest("prompts/get",e)}async setLoggingLevel(e){if(!this.initialized)throw Error("Cliente no inicializado");await this.sendNotification("notifications/logging/setLevel",{level:e})}}class p extends m{constructor(e){super(),this.process=null,this.messageBuffer="",this.config=e}async connect(){if(!this.connecting&&!this.connected){this.connecting=!0;try{await this.connectViaNodeJS()}catch(e){throw this.connecting=!1,e}}}async connectViaBrowserAPI(){try{let e=new WebSocket("ws://localhost:3001/mcp-proxy");e.onopen=()=>{e.send(JSON.stringify({type:"start_process",command:this.config.command,args:this.config.args||[],env:this.config.env||{}}))},e.onmessage=e=>{let t=JSON.parse(e.data);"process_started"===t.type?this.handleConnect():"stdout"===t.type?this.handleStdout(t.data):"stderr"===t.type?console.warn("MCP Server stderr:",t.data):"error"===t.type?this.handleError(Error(t.message)):"exit"===t.type&&this.handleDisconnect()},e.onerror=e=>{this.handleError(Error("WebSocket error: "+e))},e.onclose=()=>{this.handleDisconnect()},this.process=e,await new Promise((e,t)=>{let r=setTimeout(()=>{t(Error("Timeout connecting to MCP proxy"))},1e4),n=()=>{clearTimeout(r),this.removeListener("error",s),e()},s=e=>{clearTimeout(r),this.removeListener("connect",n),t(e)};this.once("connect",n),this.once("error",s)})}catch(e){throw Error(`Failed to connect via browser API: ${e}`)}}async connectViaNodeJS(){try{if("undefined"==typeof process||!process.versions?.node)throw Error("Node.js environment required for STDIO transport");let{spawn:e}=await Promise.resolve().then(r.t.bind(r,9646,23));this.process=e(this.config.command,this.config.args||[],{env:{...process.env,...this.config.env},stdio:["pipe","pipe","pipe"]}),this.process.stdout.on("data",e=>{this.handleStdout(e.toString())}),this.process.stderr.on("data",e=>{console.warn("MCP Server stderr:",e.toString())}),this.process.on("error",e=>{this.handleError(e)}),this.process.on("exit",e=>{0!==e?this.handleError(Error(`Process exited with code ${e}`)):this.handleDisconnect()}),await new Promise(e=>setTimeout(e,100)),this.handleConnect()}catch(e){throw Error(`Failed to spawn process: ${e}`)}}handleStdout(e){this.messageBuffer+=e;let t=this.messageBuffer.split("\n");for(let e of(this.messageBuffer=t.pop()||"",t))if(e.trim())try{let t=JSON.parse(e.trim());this.handleMessage(t)}catch(t){console.error("Error parsing MCP message:",t,"Line:",e)}}async send(e){if(!this.connected||!this.process)throw Error("Not connected");let t=JSON.stringify(e)+"\n";this.process.stdin.write(t)}async disconnect(){this.process&&(this.process.kill(),this.process=null,this.messageBuffer="",this.handleDisconnect())}}class g extends m{constructor(e){super(),this.eventSource=null,this.sessionId=null,this.lastEventId=null,this.config=e,this.messageEndpoint=e.messageEndpoint||this.config.url.replace("/sse","/messages")}async connect(){if(!this.connecting&&!this.connected){this.connecting=!0;try{await this.establishSSEConnection()}catch(e){throw this.connecting=!1,e}}}async establishSSEConnection(){return new Promise((e,t)=>{let r=new URL(this.config.url);this.lastEventId&&r.searchParams.set("Last-Event-ID",this.lastEventId),this.eventSource=new EventSource(r.toString());let n=setTimeout(()=>{this.cleanup(),t(Error("Timeout connecting to SSE endpoint"))},1e4);this.eventSource.onopen=()=>{clearTimeout(n),console.log("SSE connection opened")},this.eventSource.onmessage=e=>{try{let t=JSON.parse(e.data);e.lastEventId&&(this.lastEventId=e.lastEventId),this.handleMessage(t)}catch(e){console.error("Error parsing SSE message:",e)}},this.eventSource.addEventListener("endpoint",r=>{try{let t=JSON.parse(r.data);t.uri&&(this.messageEndpoint=t.uri),clearTimeout(n),this.handleConnect(),e()}catch(e){clearTimeout(n),t(Error("Invalid endpoint event data"))}}),this.eventSource.addEventListener("message",e=>{try{let t=JSON.parse(e.data);e.lastEventId&&(this.lastEventId=e.lastEventId),this.handleMessage(t)}catch(e){console.error("Error parsing MCP message from SSE:",e)}}),this.eventSource.onerror=e=>{clearTimeout(n),this.eventSource?.readyState===EventSource.CLOSED?this.handleDisconnect():this.handleReconnect()}})}handleReconnect(){this.connected&&(console.log("SSE connection lost, attempting to reconnect..."),setTimeout(async()=>{try{await this.establishSSEConnection(),console.log("SSE reconnection successful")}catch(e){console.error("SSE reconnection failed:",e),this.handleError(Error("Failed to reconnect to SSE endpoint"))}},1e3))}async send(e){if(!this.connected)throw Error("Not connected to SSE endpoint");try{let t=await fetch(this.messageEndpoint,{method:"POST",headers:{"Content-Type":"application/json",...this.sessionId&&{"Mcp-Session-Id":this.sessionId}},body:JSON.stringify(e)});if(!t.ok)throw Error(`HTTP ${t.status}: ${t.statusText}`);let r=t.headers.get("Mcp-Session-Id");r&&!this.sessionId&&(this.sessionId=r);let n=t.headers.get("Content-Type");if(n?.includes("application/json")){let e=await t.json();e&&this.handleMessage(e)}}catch(e){throw Error(`Failed to send message: ${e}`)}}async disconnect(){this.cleanup(),this.handleDisconnect()}cleanup(){this.eventSource&&(this.eventSource.close(),this.eventSource=null),this.sessionId=null,this.lastEventId=null}}class x extends m{constructor(e){super(),this.sessionId=null,this.protocolVersion="2025-03-26",this.sseStream=null,this.supportsSSE=!1,this.config=e}async connect(){if(!this.connecting&&!this.connected){this.connecting=!0;try{await this.detectServerCapabilities(),this.handleConnect()}catch(e){throw this.connecting=!1,e}}}async detectServerCapabilities(){try{if((await fetch(this.config.url,{method:"POST",headers:{"Content-Type":"application/json",Accept:"application/json, text/event-stream","MCP-Protocol-Version":this.protocolVersion,...this.config.headers},body:JSON.stringify({jsonrpc:"2.0",method:"ping",id:"capability-test"})})).ok)return void console.log("Server supports Streamable HTTP transport");let e=await fetch(this.config.url,{method:"GET",headers:{Accept:"text/event-stream",...this.config.headers}});if(e.ok&&e.headers.get("Content-Type")?.includes("text/event-stream")){this.supportsSSE=!0,console.log("Server supports legacy SSE transport"),await this.setupSSEStream();return}throw Error("Server does not support any known MCP transport")}catch(e){throw Error(`Failed to detect server capabilities: ${e}`)}}async setupSSEStream(){return new Promise((e,t)=>{this.sseStream=new EventSource(this.config.url);let r=setTimeout(()=>{this.cleanup(),t(Error("Timeout establishing SSE stream"))},1e4);this.sseStream.onopen=()=>{console.log("SSE stream established")},this.sseStream.addEventListener("endpoint",n=>{try{let t=JSON.parse(n.data);console.log("Received endpoint event:",t),clearTimeout(r),e()}catch(e){clearTimeout(r),t(Error("Invalid endpoint event"))}}),this.sseStream.addEventListener("message",e=>{try{let t=JSON.parse(e.data);this.handleMessage(t)}catch(e){console.error("Error parsing SSE message:",e)}}),this.sseStream.onerror=e=>{clearTimeout(r),this.sseStream?.readyState===EventSource.CLOSED?this.handleDisconnect():t(Error("SSE stream error"))}})}async send(e){if(!this.connected)throw Error("Not connected");try{let t=await fetch(this.config.url,{method:"POST",headers:{"Content-Type":"application/json",Accept:"application/json, text/event-stream","MCP-Protocol-Version":this.protocolVersion,...this.sessionId&&{"Mcp-Session-Id":this.sessionId},...this.config.headers},body:JSON.stringify(e)});if(!t.ok){if(404===t.status&&this.sessionId)throw this.sessionId=null,Error("Session expired, please reconnect");throw Error(`HTTP ${t.status}: ${t.statusText}`)}let r=t.headers.get("Mcp-Session-Id");r&&!this.sessionId&&(this.sessionId=r);let n=t.headers.get("Content-Type");if(n?.includes("text/event-stream"))await this.handleStreamingResponse(t);else if(n?.includes("application/json")){let e=await t.json();e&&this.handleMessage(e)}else if(202===t.status)return}catch(e){throw Error(`Failed to send message: ${e}`)}}async handleStreamingResponse(e){if(!e.body)throw Error("No response body for streaming response");let t=e.body.getReader(),r=new TextDecoder,n="";try{for(;;){let{done:e,value:s}=await t.read();if(e)break;n+=r.decode(s,{stream:!0});let a=this.parseSSEEvents(n);for(let e of(n=a.remainder,a.events))if(e.data)try{let t=JSON.parse(e.data);this.handleMessage(t)}catch(e){console.error("Error parsing streaming message:",e)}}}finally{t.releaseLock()}}parseSSEEvents(e){let t=[],r=e.split("\n"),n="",s={},a=0;for(;a<r.length;){let e=r[a];""===e?(void 0!==s.data&&t.push(s),s={}):e.startsWith("data: ")?s.data=e.substring(6):e.startsWith("id: ")&&(s.id=e.substring(4)),a++}return r.length>0&&!e.endsWith("\n")&&(n=r[r.length-1]),{events:t,remainder:n}}async disconnect(){if(this.sessionId)try{await fetch(this.config.url,{method:"DELETE",headers:{"Mcp-Session-Id":this.sessionId,...this.config.headers}})}catch(e){console.warn("Failed to terminate session:",e)}this.cleanup(),this.handleDisconnect()}cleanup(){this.sseStream&&(this.sseStream.close(),this.sseStream=null),this.sessionId=null,this.supportsSSE=!1}}let f={stdio:{filesystem:{type:"stdio",command:"npx",args:["-y","@modelcontextprotocol/server-filesystem","/path/to/directory"]},memory:{type:"stdio",command:"npx",args:["-y","@modelcontextprotocol/server-memory"]},python:{type:"stdio",command:"python",args:["server.py"]}},sse:{local:{type:"sse",url:"http://localhost:3000/sse"},remote:{type:"sse",url:"https://example.com/mcp/sse"}},http:{local:{type:"streamable-http",url:"http://localhost:3000/mcp"},remote:{type:"streamable-http",url:"https://api.example.com/mcp"}}},y=(0,s.createContext)(void 0);function b({children:e}){let t=function(){let[e,t]=(0,s.useState)({connections:[],logs:[]}),r=(0,s.useRef)(new Map),n=(0,s.useCallback)((e,r,n,s)=>{let a={timestamp:new Date,level:e,message:r,connectionId:n,data:s};t(e=>({...e,logs:[...e.logs.slice(-99),a]}))},[]),a=(0,s.useCallback)((e,r)=>{t(t=>({...t,connections:t.connections.map(t=>t.id===e?{...t,...r}:t)}))},[]),i=(0,s.useCallback)(async(e,r)=>{let s=u(),a={id:s,name:e,config:r,status:{connected:!1,connecting:!1},tools:[],resources:[],prompts:[]};return t(e=>({...e,connections:[...e.connections,a]})),n("info",`Conexi\xf3n creada: ${e}`,s),s},[n]),o=(0,s.useCallback)(async t=>{let s=e.connections.find(e=>e.id===t);if(!s)throw Error("Conexi\xf3n no encontrada");if(!s.status.connecting&&!s.status.connected){a(t,{status:{...s.status,connecting:!0,error:void 0}}),n("info",`Conectando a ${s.name}...`,t);try{let e=function(e){switch(e.type){case"stdio":return new p(e);case"sse":return new g(e);case"http":case"streamable-http":return new x(e);default:throw Error(`Unsupported transport type: ${e.type}`)}}(s.config),i=new h({name:"MCP Web Client",version:"1.0.0"},{roots:{listChanged:!0},sampling:{}});i.on("initialized",e=>{let r=e.serverInfo;a(t,{status:{connected:!0,connecting:!1,lastConnected:new Date,serverInfo:r}}),n("info",`Conectado a ${r.name} v${r.version}`,t)}),i.on("toolsChanged",e=>{a(t,{tools:e}),n("info",`Herramientas actualizadas: ${e.length} disponibles`,t)}),i.on("resourcesChanged",e=>{a(t,{resources:e}),n("info",`Recursos actualizados: ${e.length} disponibles`,t)}),i.on("promptsChanged",e=>{a(t,{prompts:e}),n("info",`Prompts actualizados: ${e.length} disponibles`,t)}),i.on("toolResult",(e,r)=>{n("info",`Herramienta ejecutada: ${e}`,t,r)}),i.on("log",e=>{n(e.level,e.data,t,e)}),i.on("error",e=>{n("error",`Error del cliente: ${e.message}`,t),a(t,{status:{...s.status,connecting:!1,error:e.message}})}),i.on("disconnect",()=>{n("warn","Desconectado del servidor",t),a(t,{status:{connected:!1,connecting:!1}}),r.current.delete(t)}),await i.connect(e),r.current.set(t,i)}catch(r){let e=r instanceof Error?r.message:"Error desconocido";throw n("error",`Error de conexi\xf3n: ${e}`,t),a(t,{status:{connected:!1,connecting:!1,error:e}}),r}}},[e.connections,a,n]),l=(0,s.useCallback)(async e=>{let t=r.current.get(e);t&&(await t.disconnect(),r.current.delete(e)),a(e,{status:{connected:!1,connecting:!1},tools:[],resources:[],prompts:[]}),n("info","Desconectado manualmente",e)},[a,n]),c=(0,s.useCallback)(async e=>{await l(e),t(t=>({...t,connections:t.connections.filter(t=>t.id!==e),activeConnection:t.activeConnection===e?void 0:t.activeConnection})),n("info","Conexi\xf3n eliminada",e)},[l,n]),d=(0,s.useCallback)(e=>{t(t=>({...t,activeConnection:e}))},[]),m=(0,s.useCallback)(async(e,t)=>{let s=r.current.get(e);if(!s)throw Error("Cliente no encontrado o no conectado");n("info",`Ejecutando herramienta: ${t.name}`,e,t.arguments);try{let r=await s.callTool(t);return n("info",`Herramienta completada: ${t.name}`,e,r),r}catch(s){let r=s instanceof Error?s.message:"Error desconocido";throw n("error",`Error ejecutando herramienta ${t.name}: ${r}`,e),s}},[n]),f=(0,s.useCallback)(async(e,t)=>{let s=r.current.get(e);if(!s)throw Error("Cliente no encontrado o no conectado");n("info",`Leyendo recurso: ${t.uri}`,e);try{let r=await s.readResource(t);return n("info",`Recurso le\xeddo: ${t.uri}`,e),r}catch(s){let r=s instanceof Error?s.message:"Error desconocido";throw n("error",`Error leyendo recurso ${t.uri}: ${r}`,e),s}},[n]),y=(0,s.useCallback)(async(e,t)=>{let s=r.current.get(e);if(!s)throw Error("Cliente no encontrado o no conectado");n("info",`Obteniendo prompt: ${t.name}`,e,t.arguments);try{let r=await s.getPrompt(t);return n("info",`Prompt obtenido: ${t.name}`,e),r}catch(s){let r=s instanceof Error?s.message:"Error desconocido";throw n("error",`Error obteniendo prompt ${t.name}: ${r}`,e),s}},[n]),b=(0,s.useCallback)(()=>{t(e=>({...e,logs:[]}))},[]);return{connections:e.connections,activeConnection:e.activeConnection,logs:e.logs,createConnection:i,connectToServer:o,disconnectFromServer:l,removeConnection:c,setActiveConnection:d,callTool:m,readResource:f,getPrompt:y,clearLogs:b,getActiveConnection:()=>e.connections.find(t=>t.id===e.activeConnection),getConnectedClients:()=>e.connections.filter(e=>e.status.connected),isConnected:t=>{let r=e.connections.find(e=>e.id===t);return r?.status.connected||!1}}}();return(0,n.jsx)(y.Provider,{value:t,children:e})}function v(){let e=(0,s.useContext)(y);if(void 0===e)throw Error("useMCPClientContext must be used within a MCPClientProvider");return e}function j(){let{connections:e,activeConnection:t,connectToServer:r,disconnectFromServer:a,removeConnection:i,setActiveConnection:o}=v(),[l,c]=(0,s.useState)(new Set),d=async e=>{c(t=>new Set(t).add(e));try{await r(e)}catch(e){console.error("Error connecting:",e)}finally{c(t=>{let r=new Set(t);return r.delete(e),r})}},u=async e=>{c(t=>new Set(t).add(e));try{await a(e)}catch(e){console.error("Error disconnecting:",e)}finally{c(t=>{let r=new Set(t);return r.delete(e),r})}},m=async e=>{if(confirm("\xbfEst\xe1s seguro de que quieres eliminar esta conexi\xf3n?"))try{await i(e)}catch(e){console.error("Error removing connection:",e)}},h=e=>e.status.connecting?"text-yellow-600 bg-yellow-100 dark:bg-yellow-900 dark:text-yellow-300":e.status.connected?"text-green-600 bg-green-100 dark:bg-green-900 dark:text-green-300":e.status.error?"text-red-600 bg-red-100 dark:bg-red-900 dark:text-red-300":"text-gray-600 bg-gray-100 dark:bg-gray-700 dark:text-gray-300",p=e=>e.status.connecting?"Conectando...":e.status.connected?"Conectado":e.status.error?"Error":"Desconectado",g=e=>{switch(e){case"stdio":return(0,n.jsx)("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,n.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"})});case"sse":return(0,n.jsx)("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,n.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M13 10V3L4 14h7v7l9-11h-7z"})});case"http":case"streamable-http":return(0,n.jsx)("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,n.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9a9 9 0 01-9-9m9 9c1.657 0 3-4.03 3-9s-1.343-9-3-9m0 18c-1.657 0-3-4.03-3-9s1.343-9 3-9m-9 9a9 9 0 019-9"})});default:return(0,n.jsx)("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,n.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M8.111 16.404a5.5 5.5 0 017.778 0M12 20h.01m-7.08-7.071c3.904-3.905 10.236-3.905 14.141 0M1.394 9.393c5.857-5.857 15.355-5.857 21.213 0"})})}};return 0===e.length?(0,n.jsxs)("div",{className:"text-center py-12",children:[(0,n.jsx)("svg",{className:"mx-auto h-12 w-12 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,n.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M8.111 16.404a5.5 5.5 0 017.778 0M12 20h.01m-7.08-7.071c3.904-3.905 10.236-3.905 14.141 0M1.394 9.393c5.857-5.857 15.355-5.857 21.213 0"})}),(0,n.jsx)("h3",{className:"mt-2 text-sm font-medium text-gray-900 dark:text-white",children:"No hay conexiones"}),(0,n.jsx)("p",{className:"mt-1 text-sm text-gray-500 dark:text-gray-400",children:"Crea una nueva conexi\xf3n para comenzar a usar MCP."})]}):(0,n.jsx)("div",{className:"space-y-4",children:e.map(e=>(0,n.jsxs)("div",{className:`border rounded-lg p-4 transition-all ${t===e.id?"border-blue-500 bg-blue-50 dark:bg-blue-900/20":"border-gray-200 dark:border-gray-700 hover:border-gray-300 dark:hover:border-gray-600"}`,children:[(0,n.jsxs)("div",{className:"flex items-center justify-between",children:[(0,n.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,n.jsxs)("div",{className:"flex items-center space-x-2",children:[g(e.config.type),(0,n.jsx)("h3",{className:"text-lg font-medium text-gray-900 dark:text-white",children:e.name})]}),(0,n.jsx)("span",{className:`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${h(e)}`,children:p(e)})]}),(0,n.jsxs)("div",{className:"flex items-center space-x-2",children:[!e.status.connected&&!e.status.connecting&&(0,n.jsx)("button",{onClick:()=>d(e.id),disabled:l.has(e.id),className:"bg-green-600 hover:bg-green-700 disabled:bg-green-400 text-white px-3 py-1 rounded text-sm font-medium transition-colors",children:l.has(e.id)?"Conectando...":"Conectar"}),e.status.connected&&(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)("button",{onClick:()=>o(e.id),className:`px-3 py-1 rounded text-sm font-medium transition-colors ${t===e.id?"bg-blue-600 text-white":"bg-gray-200 hover:bg-gray-300 text-gray-700 dark:bg-gray-600 dark:hover:bg-gray-500 dark:text-gray-200"}`,children:t===e.id?"Activa":"Activar"}),(0,n.jsx)("button",{onClick:()=>u(e.id),disabled:l.has(e.id),className:"bg-red-600 hover:bg-red-700 disabled:bg-red-400 text-white px-3 py-1 rounded text-sm font-medium transition-colors",children:l.has(e.id)?"Desconectando...":"Desconectar"})]}),(0,n.jsx)("button",{onClick:()=>m(e.id),className:"text-gray-400 hover:text-red-600 dark:hover:text-red-400 p-1",title:"Eliminar conexi\xf3n",children:(0,n.jsx)("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,n.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"})})})]})]}),(0,n.jsxs)("div",{className:"mt-3 text-sm text-gray-600 dark:text-gray-300",children:[(0,n.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-2",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("span",{className:"font-medium",children:"Tipo:"})," ",e.config.type.toUpperCase()]}),e.config.url&&(0,n.jsxs)("div",{children:[(0,n.jsx)("span",{className:"font-medium",children:"URL:"})," ",e.config.url]}),e.config.command&&(0,n.jsxs)("div",{children:[(0,n.jsx)("span",{className:"font-medium",children:"Comando:"})," ",e.config.command,e.config.args&&e.config.args.length>0&&(0,n.jsx)("span",{className:"ml-1",children:e.config.args.join(" ")})]}),e.status.serverInfo&&(0,n.jsxs)("div",{children:[(0,n.jsx)("span",{className:"font-medium",children:"Servidor:"})," ",e.status.serverInfo.name," v",e.status.serverInfo.version]}),e.status.lastConnected&&(0,n.jsxs)("div",{children:[(0,n.jsx)("span",{className:"font-medium",children:"\xdaltima conexi\xf3n:"})," ",e.status.lastConnected.toLocaleString()]})]}),e.status.error&&(0,n.jsxs)("div",{className:"mt-2 p-2 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded text-red-700 dark:text-red-300 text-xs",children:[(0,n.jsx)("span",{className:"font-medium",children:"Error:"})," ",e.status.error]}),e.status.connected&&(0,n.jsxs)("div",{className:"mt-2 flex space-x-4 text-xs",children:[(0,n.jsxs)("span",{children:[(0,n.jsx)("span",{className:"font-medium",children:"Herramientas:"})," ",e.tools.length]}),(0,n.jsxs)("span",{children:[(0,n.jsx)("span",{className:"font-medium",children:"Recursos:"})," ",e.resources.length]}),(0,n.jsxs)("span",{children:[(0,n.jsx)("span",{className:"font-medium",children:"Prompts:"})," ",e.prompts.length]})]})]})]},e.id))})}function k({onSuccess:e,onCancel:t}){let{createConnection:r,connectToServer:a}=v(),[i,o]=(0,s.useState)({name:"",type:"stdio",url:"",command:"",args:"",env:""}),[l,c]=(0,s.useState)(!1),[d,u]=(0,s.useState)(null),m=async t=>{t.preventDefault(),c(!0),u(null);try{if(!i.name.trim())throw Error("El nombre es requerido");let t={type:i.type};if("stdio"===i.type){if(!i.command.trim())throw Error("El comando es requerido para conexiones STDIO");t.command=i.command.trim(),t.args=i.args.trim()?i.args.trim().split(" "):[],t.env=i.env.trim()?JSON.parse(i.env):{}}else{if(!i.url.trim())throw Error("La URL es requerida para conexiones HTTP/SSE");t.url=i.url.trim()}let n=await r(i.name.trim(),t);try{await a(n)}catch(e){console.warn("Failed to auto-connect:",e)}e()}catch(e){u(e instanceof Error?e.message:"Error desconocido")}finally{c(!1)}},h=e=>{o({name:"",type:e.type,url:e.url||"",command:e.command||"",args:e.args?e.args.join(" "):"",env:e.env?JSON.stringify(e.env,null,2):""})};return(0,n.jsxs)("form",{onSubmit:m,className:"space-y-6",children:[d&&(0,n.jsx)("div",{className:"bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md p-4",children:(0,n.jsx)("div",{className:"text-red-700 dark:text-red-300 text-sm",children:d})}),(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{htmlFor:"name",className:"block text-sm font-medium text-gray-700 dark:text-gray-300",children:"Nombre de la conexi\xf3n"}),(0,n.jsx)("input",{type:"text",id:"name",value:i.name,onChange:e=>o({...i,name:e.target.value}),className:"mt-1 block w-full border border-gray-300 dark:border-gray-600 rounded-md px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"Mi servidor MCP",required:!0})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{htmlFor:"type",className:"block text-sm font-medium text-gray-700 dark:text-gray-300",children:"Tipo de transporte"}),(0,n.jsxs)("select",{id:"type",value:i.type,onChange:e=>o({...i,type:e.target.value}),className:"mt-1 block w-full border border-gray-300 dark:border-gray-600 rounded-md px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500",children:[(0,n.jsx)("option",{value:"stdio",children:"STDIO (Proceso local)"}),(0,n.jsx)("option",{value:"sse",children:"SSE (Server-Sent Events)"}),(0,n.jsx)("option",{value:"streamable-http",children:"HTTP Streamable"})]})]}),"stdio"===i.type?(0,n.jsxs)(n.Fragment,{children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{htmlFor:"command",className:"block text-sm font-medium text-gray-700 dark:text-gray-300",children:"Comando"}),(0,n.jsx)("input",{type:"text",id:"command",value:i.command,onChange:e=>o({...i,command:e.target.value}),className:"mt-1 block w-full border border-gray-300 dark:border-gray-600 rounded-md px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"npx, python, node, etc.",required:!0})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{htmlFor:"args",className:"block text-sm font-medium text-gray-700 dark:text-gray-300",children:"Argumentos (separados por espacios)"}),(0,n.jsx)("input",{type:"text",id:"args",value:i.args,onChange:e=>o({...i,args:e.target.value}),className:"mt-1 block w-full border border-gray-300 dark:border-gray-600 rounded-md px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"-y @modelcontextprotocol/server-memory"})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{htmlFor:"env",className:"block text-sm font-medium text-gray-700 dark:text-gray-300",children:"Variables de entorno (JSON)"}),(0,n.jsx)("textarea",{id:"env",value:i.env,onChange:e=>o({...i,env:e.target.value}),rows:3,className:"mt-1 block w-full border border-gray-300 dark:border-gray-600 rounded-md px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:'{"API_KEY": "valor"}'})]})]}):(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{htmlFor:"url",className:"block text-sm font-medium text-gray-700 dark:text-gray-300",children:"URL del servidor"}),(0,n.jsx)("input",{type:"url",id:"url",value:i.url,onChange:e=>o({...i,url:e.target.value}),className:"mt-1 block w-full border border-gray-300 dark:border-gray-600 rounded-md px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:"http://localhost:3000/mcp",required:!0})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Configuraciones predefinidas"}),(0,n.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-2",children:["stdio"===i.type&&(0,n.jsxs)(n.Fragment,{children:[(0,n.jsxs)("button",{type:"button",onClick:()=>h(f.stdio.filesystem),className:"text-left p-2 border border-gray-300 dark:border-gray-600 rounded hover:bg-gray-50 dark:hover:bg-gray-700 text-sm",children:[(0,n.jsx)("div",{className:"font-medium",children:"Filesystem"}),(0,n.jsx)("div",{className:"text-gray-500 dark:text-gray-400",children:"Acceso al sistema de archivos"})]}),(0,n.jsxs)("button",{type:"button",onClick:()=>h(f.stdio.memory),className:"text-left p-2 border border-gray-300 dark:border-gray-600 rounded hover:bg-gray-50 dark:hover:bg-gray-700 text-sm",children:[(0,n.jsx)("div",{className:"font-medium",children:"Memory"}),(0,n.jsx)("div",{className:"text-gray-500 dark:text-gray-400",children:"Almacenamiento en memoria"})]})]}),("sse"===i.type||"streamable-http"===i.type)&&(0,n.jsx)(n.Fragment,{children:(0,n.jsxs)("button",{type:"button",onClick:()=>h("sse"===i.type?f.sse.local:f.http.local),className:"text-left p-2 border border-gray-300 dark:border-gray-600 rounded hover:bg-gray-50 dark:hover:bg-gray-700 text-sm",children:[(0,n.jsx)("div",{className:"font-medium",children:"Servidor local"}),(0,n.jsx)("div",{className:"text-gray-500 dark:text-gray-400",children:"localhost:3000"})]})})]})]}),(0,n.jsxs)("div",{className:"flex justify-end space-x-3",children:[(0,n.jsx)("button",{type:"button",onClick:t,className:"px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors",children:"Cancelar"}),(0,n.jsx)("button",{type:"submit",disabled:l,className:"px-4 py-2 bg-blue-600 hover:bg-blue-700 disabled:bg-blue-400 text-white rounded-md transition-colors",children:l?"Creando...":"Crear Conexi\xf3n"})]})]})}function N(){let{getActiveConnection:e,callTool:t}=v(),[r,a]=(0,s.useState)(null),[i,o]=(0,s.useState)({}),[l,c]=(0,s.useState)(!1),[d,u]=(0,s.useState)(null),[m,h]=(0,s.useState)(null),p=e(),g=p?.tools||[],x=e=>{a(e),u(null),h(null);let t={};e.inputSchema.properties&&Object.keys(e.inputSchema.properties).forEach(r=>{let n=e.inputSchema.properties[r];void 0!==n.default?t[r]=n.default:"string"===n.type?t[r]="":"number"===n.type?t[r]=0:"boolean"===n.type&&(t[r]=!1)}),o(t)},f=async()=>{if(r&&p){c(!0),h(null),u(null);try{let e={name:r.name,arguments:i},n=await t(p.id,e);u(n)}catch(e){h(e instanceof Error?e.message:"Error desconocido")}finally{c(!1)}}},y=(e,t)=>{let s=r?.inputSchema.required?.includes(e)||!1;switch(t.type){case"string":if(t.enum)return(0,n.jsxs)("select",{value:i[e]||"",onChange:t=>o({...i,[e]:t.target.value}),className:"mt-1 block w-full border border-gray-300 dark:border-gray-600 rounded-md px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500",required:s,children:[(0,n.jsx)("option",{value:"",children:"Seleccionar..."}),t.enum.map(e=>(0,n.jsx)("option",{value:e,children:e},e))]});return(0,n.jsx)("input",{type:"text",value:i[e]||"",onChange:t=>o({...i,[e]:t.target.value}),className:"mt-1 block w-full border border-gray-300 dark:border-gray-600 rounded-md px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:t.description||`Ingrese ${e}`,required:s});case"number":return(0,n.jsx)("input",{type:"number",value:i[e]||"",onChange:t=>o({...i,[e]:parseFloat(t.target.value)||0}),className:"mt-1 block w-full border border-gray-300 dark:border-gray-600 rounded-md px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:t.description||`Ingrese ${e}`,required:s});case"boolean":return(0,n.jsxs)("label",{className:"flex items-center mt-1",children:[(0,n.jsx)("input",{type:"checkbox",checked:i[e]||!1,onChange:t=>o({...i,[e]:t.target.checked}),className:"rounded border-gray-300 dark:border-gray-600 text-blue-600 focus:ring-blue-500"}),(0,n.jsx)("span",{className:"ml-2 text-sm text-gray-700 dark:text-gray-300",children:t.description||e})]});default:return(0,n.jsx)("textarea",{value:"object"==typeof i[e]?JSON.stringify(i[e],null,2):i[e]||"",onChange:t=>{try{let r=t.target.value;o({...i,[e]:r.startsWith("{")||r.startsWith("[")?JSON.parse(r):r})}catch{o({...i,[e]:t.target.value})}},rows:3,className:"mt-1 block w-full border border-gray-300 dark:border-gray-600 rounded-md px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:t.description||`Ingrese ${e} (JSON si es objeto/array)`,required:s})}};return p?0===g.length?(0,n.jsxs)("div",{className:"text-center py-8",children:[(0,n.jsxs)("svg",{className:"mx-auto h-12 w-12 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:[(0,n.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"}),(0,n.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M15 12a3 3 0 11-6 0 3 3 0 016 0z"})]}),(0,n.jsx)("h3",{className:"mt-2 text-sm font-medium text-gray-900 dark:text-white",children:"No hay herramientas disponibles"}),(0,n.jsx)("p",{className:"mt-1 text-sm text-gray-500 dark:text-gray-400",children:"El servidor conectado no expone ninguna herramienta."})]}):(0,n.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,n.jsxs)("div",{children:[(0,n.jsxs)("h3",{className:"text-lg font-medium text-gray-900 dark:text-white mb-4",children:["Herramientas Disponibles (",g.length,")"]}),(0,n.jsx)("div",{className:"space-y-3",children:g.map(e=>(0,n.jsxs)("div",{onClick:()=>x(e),className:`p-4 border rounded-lg cursor-pointer transition-all ${r?.name===e.name?"border-blue-500 bg-blue-50 dark:bg-blue-900/20":"border-gray-200 dark:border-gray-700 hover:border-gray-300 dark:hover:border-gray-600"}`,children:[(0,n.jsx)("h4",{className:"font-medium text-gray-900 dark:text-white",children:e.name}),e.description&&(0,n.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-300 mt-1",children:e.description}),e.inputSchema.required&&e.inputSchema.required.length>0&&(0,n.jsx)("div",{className:"mt-2",children:(0,n.jsxs)("span",{className:"text-xs text-gray-500 dark:text-gray-400",children:["Par\xe1metros requeridos: ",e.inputSchema.required.join(", ")]})})]},e.name))})]}),(0,n.jsx)("div",{children:r?(0,n.jsxs)("div",{children:[(0,n.jsxs)("h3",{className:"text-lg font-medium text-gray-900 dark:text-white mb-4",children:["Ejecutar: ",r.name]}),r.description&&(0,n.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-300 mb-4",children:r.description}),r.inputSchema.properties&&Object.keys(r.inputSchema.properties).length>0&&(0,n.jsxs)("div",{className:"space-y-4 mb-6",children:[(0,n.jsx)("h4",{className:"font-medium text-gray-900 dark:text-white",children:"Par\xe1metros"}),Object.entries(r.inputSchema.properties).map(([e,t])=>(0,n.jsxs)("div",{children:[(0,n.jsxs)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300",children:[e,r.inputSchema.required?.includes(e)&&(0,n.jsx)("span",{className:"text-red-500 ml-1",children:"*"})]}),y(e,t),t.description&&(0,n.jsx)("p",{className:"mt-1 text-xs text-gray-500 dark:text-gray-400",children:t.description})]},e))]}),(0,n.jsx)("button",{onClick:f,disabled:l,className:"w-full bg-blue-600 hover:bg-blue-700 disabled:bg-blue-400 text-white py-2 px-4 rounded-lg font-medium transition-colors",children:l?"Ejecutando...":"Ejecutar Herramienta"}),m&&(0,n.jsxs)("div",{className:"mt-4 p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg",children:[(0,n.jsx)("h4",{className:"font-medium text-red-800 dark:text-red-300 mb-2",children:"Error"}),(0,n.jsx)("p",{className:"text-sm text-red-700 dark:text-red-300",children:m})]}),d&&(0,n.jsxs)("div",{className:"mt-4 p-4 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg",children:[(0,n.jsx)("h4",{className:"font-medium text-green-800 dark:text-green-300 mb-2",children:"Resultado"}),(0,n.jsx)("div",{className:"space-y-2",children:d.content.map((e,t)=>(0,n.jsxs)("div",{className:"text-sm",children:["text"===e.type&&(0,n.jsx)("pre",{className:"whitespace-pre-wrap text-green-700 dark:text-green-300 bg-green-100 dark:bg-green-900/40 p-2 rounded",children:e.text}),"image"===e.type&&(0,n.jsxs)("div",{children:[(0,n.jsx)("p",{className:"text-green-700 dark:text-green-300 mb-1",children:"Imagen:"}),(0,n.jsx)("img",{src:`data:${e.mimeType};base64,${e.data}`,alt:"Tool result",className:"max-w-full h-auto rounded"})]}),"resource"===e.type&&(0,n.jsx)("div",{children:(0,n.jsxs)("p",{className:"text-green-700 dark:text-green-300",children:["Recurso: ",e.text]})})]},t))}),d.isError&&(0,n.jsx)("p",{className:"text-xs text-red-600 dark:text-red-400 mt-2",children:"⚠️ La herramienta report\xf3 un error"})]})]}):(0,n.jsx)("div",{className:"text-center py-8",children:(0,n.jsx)("p",{className:"text-gray-500 dark:text-gray-400",children:"Selecciona una herramienta para ejecutarla."})})})]}):(0,n.jsx)("div",{className:"text-center py-8",children:(0,n.jsx)("p",{className:"text-gray-500 dark:text-gray-400",children:"Selecciona una conexi\xf3n activa para ver las herramientas disponibles."})})}function E(){let{getActiveConnection:e,readResource:t}=v(),[r,a]=(0,s.useState)(null),[i,o]=(0,s.useState)(!1),[l,c]=(0,s.useState)(null),[d,u]=(0,s.useState)(null),m=e(),h=m?.resources||[],p=async e=>{if(m){a(e),o(!0),u(null),c(null);try{let r=await t(m.id,{uri:e.uri});c(r)}catch(e){u(e instanceof Error?e.message:"Error desconocido")}finally{o(!1)}}};return m?0===h.length?(0,n.jsxs)("div",{className:"text-center py-8",children:[(0,n.jsx)("svg",{className:"mx-auto h-12 w-12 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,n.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"})}),(0,n.jsx)("h3",{className:"mt-2 text-sm font-medium text-gray-900 dark:text-white",children:"No hay recursos disponibles"}),(0,n.jsx)("p",{className:"mt-1 text-sm text-gray-500 dark:text-gray-400",children:"El servidor conectado no expone ning\xfan recurso."})]}):(0,n.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,n.jsxs)("div",{children:[(0,n.jsxs)("h3",{className:"text-lg font-medium text-gray-900 dark:text-white mb-4",children:["Recursos Disponibles (",h.length,")"]}),(0,n.jsx)("div",{className:"space-y-3",children:h.map(e=>(0,n.jsxs)("div",{onClick:()=>p(e),className:`p-4 border rounded-lg cursor-pointer transition-all ${r?.uri===e.uri?"border-blue-500 bg-blue-50 dark:bg-blue-900/20":"border-gray-200 dark:border-gray-700 hover:border-gray-300 dark:hover:border-gray-600"}`,children:[(0,n.jsx)("h4",{className:"font-medium text-gray-900 dark:text-white",children:e.name}),(0,n.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-300 mt-1 font-mono",children:e.uri}),e.description&&(0,n.jsx)("p",{className:"text-sm text-gray-500 dark:text-gray-400 mt-1",children:e.description}),e.mimeType&&(0,n.jsx)("span",{className:"inline-block mt-2 px-2 py-1 bg-gray-100 dark:bg-gray-700 text-xs text-gray-600 dark:text-gray-300 rounded",children:e.mimeType})]},e.uri))})]}),(0,n.jsx)("div",{children:r?(0,n.jsxs)("div",{children:[(0,n.jsx)("h3",{className:"text-lg font-medium text-gray-900 dark:text-white mb-4",children:r.name}),(0,n.jsxs)("div",{className:"mb-4 p-3 bg-gray-50 dark:bg-gray-700 rounded-lg",children:[(0,n.jsxs)("p",{className:"text-sm text-gray-600 dark:text-gray-300",children:[(0,n.jsx)("span",{className:"font-medium",children:"URI:"})," ",r.uri]}),r.mimeType&&(0,n.jsxs)("p",{className:"text-sm text-gray-600 dark:text-gray-300 mt-1",children:[(0,n.jsx)("span",{className:"font-medium",children:"Tipo:"})," ",r.mimeType]}),r.description&&(0,n.jsxs)("p",{className:"text-sm text-gray-600 dark:text-gray-300 mt-1",children:[(0,n.jsx)("span",{className:"font-medium",children:"Descripci\xf3n:"})," ",r.description]})]}),i&&(0,n.jsxs)("div",{className:"text-center py-8",children:[(0,n.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"}),(0,n.jsx)("p",{className:"text-sm text-gray-500 dark:text-gray-400 mt-2",children:"Cargando recurso..."})]}),d&&(0,n.jsxs)("div",{className:"p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg",children:[(0,n.jsx)("h4",{className:"font-medium text-red-800 dark:text-red-300 mb-2",children:"Error"}),(0,n.jsx)("p",{className:"text-sm text-red-700 dark:text-red-300",children:d})]}),l&&(0,n.jsx)("div",{className:"space-y-4",children:l.contents.map((e,t)=>(0,n.jsxs)("div",{className:"border border-gray-200 dark:border-gray-700 rounded-lg overflow-hidden",children:[(0,n.jsx)("div",{className:"bg-gray-50 dark:bg-gray-700 px-4 py-2 border-b border-gray-200 dark:border-gray-600",children:(0,n.jsxs)("p",{className:"text-sm font-medium text-gray-900 dark:text-white",children:["Contenido ",t+1,e.mimeType&&(0,n.jsxs)("span",{className:"ml-2 text-xs text-gray-500 dark:text-gray-400",children:["(",e.mimeType,")"]})]})}),(0,n.jsxs)("div",{className:"p-4",children:[e.text&&(0,n.jsx)("div",{children:e.mimeType?.includes("json")?(0,n.jsx)("pre",{className:"text-sm bg-gray-100 dark:bg-gray-800 p-3 rounded overflow-x-auto",children:JSON.stringify(JSON.parse(e.text),null,2)}):e.mimeType?.includes("html")?(0,n.jsx)("div",{className:"prose dark:prose-invert max-w-none",children:(0,n.jsx)("div",{dangerouslySetInnerHTML:{__html:e.text}})}):(0,n.jsx)("pre",{className:"text-sm bg-gray-100 dark:bg-gray-800 p-3 rounded overflow-x-auto whitespace-pre-wrap",children:e.text})}),e.blob&&(0,n.jsx)("div",{children:e.mimeType?.startsWith("image/")?(0,n.jsx)("img",{src:`data:${e.mimeType};base64,${e.blob}`,alt:"Resource content",className:"max-w-full h-auto rounded"}):(0,n.jsxs)("div",{className:"text-center py-4",children:[(0,n.jsxs)("p",{className:"text-sm text-gray-500 dark:text-gray-400",children:["Contenido binario (",e.mimeType,")"]}),(0,n.jsx)("button",{onClick:()=>{let t=document.createElement("a");t.href=`data:${e.mimeType};base64,${e.blob}`,t.download=r.name||"resource",t.click()},className:"mt-2 bg-blue-600 hover:bg-blue-700 text-white px-3 py-1 rounded text-sm",children:"Descargar"})]})})]})]},t))})]}):(0,n.jsx)("div",{className:"text-center py-8",children:(0,n.jsx)("p",{className:"text-gray-500 dark:text-gray-400",children:"Selecciona un recurso para ver su contenido."})})})]}):(0,n.jsx)("div",{className:"text-center py-8",children:(0,n.jsx)("p",{className:"text-gray-500 dark:text-gray-400",children:"Selecciona una conexi\xf3n activa para ver los recursos disponibles."})})}function w(){let{getActiveConnection:e,getPrompt:t}=v(),[r,a]=(0,s.useState)(null),[i,o]=(0,s.useState)({}),[l,c]=(0,s.useState)(!1),[d,u]=(0,s.useState)(null),[m,h]=(0,s.useState)(null),p=e(),g=p?.prompts||[],x=e=>{a(e),u(null),h(null);let t={};e.arguments&&e.arguments.forEach(e=>{t[e.name]=""}),o(t)},f=async()=>{if(r&&p){c(!0),h(null),u(null);try{let e=await t(p.id,{name:r.name,arguments:i});u(e)}catch(e){h(e instanceof Error?e.message:"Error desconocido")}finally{c(!1)}}};return p?0===g.length?(0,n.jsxs)("div",{className:"text-center py-8",children:[(0,n.jsx)("svg",{className:"mx-auto h-12 w-12 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,n.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z"})}),(0,n.jsx)("h3",{className:"mt-2 text-sm font-medium text-gray-900 dark:text-white",children:"No hay prompts disponibles"}),(0,n.jsx)("p",{className:"mt-1 text-sm text-gray-500 dark:text-gray-400",children:"El servidor conectado no expone ning\xfan prompt."})]}):(0,n.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,n.jsxs)("div",{children:[(0,n.jsxs)("h3",{className:"text-lg font-medium text-gray-900 dark:text-white mb-4",children:["Prompts Disponibles (",g.length,")"]}),(0,n.jsx)("div",{className:"space-y-3",children:g.map(e=>(0,n.jsxs)("div",{onClick:()=>x(e),className:`p-4 border rounded-lg cursor-pointer transition-all ${r?.name===e.name?"border-blue-500 bg-blue-50 dark:bg-blue-900/20":"border-gray-200 dark:border-gray-700 hover:border-gray-300 dark:hover:border-gray-600"}`,children:[(0,n.jsx)("h4",{className:"font-medium text-gray-900 dark:text-white",children:e.name}),e.description&&(0,n.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-300 mt-1",children:e.description}),e.arguments&&e.arguments.length>0&&(0,n.jsx)("div",{className:"mt-2",children:(0,n.jsxs)("span",{className:"text-xs text-gray-500 dark:text-gray-400",children:["Argumentos: ",e.arguments.map(e=>e.name).join(", ")]})})]},e.name))})]}),(0,n.jsx)("div",{children:r?(0,n.jsxs)("div",{children:[(0,n.jsxs)("h3",{className:"text-lg font-medium text-gray-900 dark:text-white mb-4",children:["Prompt: ",r.name]}),r.description&&(0,n.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-300 mb-4",children:r.description}),r.arguments&&r.arguments.length>0&&(0,n.jsxs)("div",{className:"space-y-4 mb-6",children:[(0,n.jsx)("h4",{className:"font-medium text-gray-900 dark:text-white",children:"Argumentos"}),r.arguments.map(e=>(0,n.jsxs)("div",{children:[(0,n.jsxs)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300",children:[e.name,e.required&&(0,n.jsx)("span",{className:"text-red-500 ml-1",children:"*"})]}),(0,n.jsx)("input",{type:"text",value:i[e.name]||"",onChange:t=>o({...i,[e.name]:t.target.value}),className:"mt-1 block w-full border border-gray-300 dark:border-gray-600 rounded-md px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500",placeholder:e.description||`Ingrese ${e.name}`,required:e.required}),e.description&&(0,n.jsx)("p",{className:"mt-1 text-xs text-gray-500 dark:text-gray-400",children:e.description})]},e.name))]}),(0,n.jsx)("button",{onClick:f,disabled:l,className:"w-full bg-blue-600 hover:bg-blue-700 disabled:bg-blue-400 text-white py-2 px-4 rounded-lg font-medium transition-colors",children:l?"Generando...":"Generar Prompt"}),m&&(0,n.jsxs)("div",{className:"mt-4 p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg",children:[(0,n.jsx)("h4",{className:"font-medium text-red-800 dark:text-red-300 mb-2",children:"Error"}),(0,n.jsx)("p",{className:"text-sm text-red-700 dark:text-red-300",children:m})]}),d&&(0,n.jsxs)("div",{className:"mt-4 space-y-4",children:[d.description&&(0,n.jsxs)("div",{className:"p-3 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg",children:[(0,n.jsx)("h4",{className:"font-medium text-blue-800 dark:text-blue-300 mb-1",children:"Descripci\xf3n"}),(0,n.jsx)("p",{className:"text-sm text-blue-700 dark:text-blue-300",children:d.description})]}),(0,n.jsxs)("div",{className:"p-4 bg-gray-50 dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg",children:[(0,n.jsx)("h4",{className:"font-medium text-gray-900 dark:text-white mb-3",children:"Mensajes Generados"}),(0,n.jsx)("div",{className:"space-y-3",children:d.messages.map((e,t)=>(0,n.jsxs)("div",{className:"border border-gray-200 dark:border-gray-600 rounded-lg overflow-hidden",children:[(0,n.jsx)("div",{className:"bg-gray-100 dark:bg-gray-700 px-3 py-2",children:(0,n.jsx)("span",{className:`text-sm font-medium ${"user"===e.role?"text-blue-600 dark:text-blue-400":"assistant"===e.role?"text-green-600 dark:text-green-400":"text-purple-600 dark:text-purple-400"}`,children:e.role.charAt(0).toUpperCase()+e.role.slice(1)})}),(0,n.jsxs)("div",{className:"p-3",children:["text"===e.content.type&&(0,n.jsx)("p",{className:"text-sm text-gray-900 dark:text-white whitespace-pre-wrap",children:e.content.text}),"image"===e.content.type&&(0,n.jsxs)("div",{children:[(0,n.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-300 mb-2",children:"Imagen:"}),(0,n.jsx)("img",{src:`data:${e.content.mimeType};base64,${e.content.data}`,alt:"Prompt content",className:"max-w-full h-auto rounded"})]}),"resource"===e.content.type&&(0,n.jsx)("div",{children:(0,n.jsxs)("p",{className:"text-sm text-gray-600 dark:text-gray-300",children:["Recurso: ",e.content.text]})})]})]},t))})]})]})]}):(0,n.jsx)("div",{className:"text-center py-8",children:(0,n.jsx)("p",{className:"text-gray-500 dark:text-gray-400",children:"Selecciona un prompt para generarlo."})})})]}):(0,n.jsx)("div",{className:"text-center py-8",children:(0,n.jsx)("p",{className:"text-gray-500 dark:text-gray-400",children:"Selecciona una conexi\xf3n activa para ver los prompts disponibles."})})}function S(){let{logs:e,clearLogs:t,connections:r}=v(),[a,i]=(0,s.useState)({level:"all",connectionId:"all",search:""}),[o,l]=(0,s.useState)(!0),c=(0,s.useRef)(null),d=e.filter(e=>("all"===a.level||e.level===a.level)&&("all"===a.connectionId||e.connectionId===a.connectionId)&&(!a.search||!!e.message.toLowerCase().includes(a.search.toLowerCase()))),u=e=>{switch(e){case"error":return"text-red-600 bg-red-100 dark:bg-red-900/20 dark:text-red-300";case"warn":return"text-yellow-600 bg-yellow-100 dark:bg-yellow-900/20 dark:text-yellow-300";case"info":return"text-blue-600 bg-blue-100 dark:bg-blue-900/20 dark:text-blue-300";default:return"text-gray-600 bg-gray-100 dark:bg-gray-700 dark:text-gray-300"}},m=e=>{switch(e){case"error":return(0,n.jsx)("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,n.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})});case"warn":return(0,n.jsx)("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,n.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"})});case"info":return(0,n.jsx)("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,n.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})});case"debug":return(0,n.jsxs)("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:[(0,n.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"}),(0,n.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M15 12a3 3 0 11-6 0 3 3 0 016 0z"})]});default:return null}},h=e=>{if(!e)return"Sistema";let t=r.find(t=>t.id===e);return t?.name||"Conexi\xf3n desconocida"};return(0,n.jsxs)("div",{className:"space-y-4",children:[(0,n.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between",children:[(0,n.jsxs)("div",{className:"flex flex-col sm:flex-row gap-3 flex-1",children:[(0,n.jsxs)("select",{value:a.level,onChange:e=>i({...a,level:e.target.value}),className:"border border-gray-300 dark:border-gray-600 rounded-md px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white text-sm",children:[(0,n.jsx)("option",{value:"all",children:"Todos los niveles"}),(0,n.jsx)("option",{value:"error",children:"Error"}),(0,n.jsx)("option",{value:"warn",children:"Advertencia"}),(0,n.jsx)("option",{value:"info",children:"Informaci\xf3n"}),(0,n.jsx)("option",{value:"debug",children:"Debug"})]}),(0,n.jsxs)("select",{value:a.connectionId,onChange:e=>i({...a,connectionId:e.target.value}),className:"border border-gray-300 dark:border-gray-600 rounded-md px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white text-sm",children:[(0,n.jsx)("option",{value:"all",children:"Todas las conexiones"}),(0,n.jsx)("option",{value:"",children:"Sistema"}),r.map(e=>(0,n.jsx)("option",{value:e.id,children:e.name},e.id))]}),(0,n.jsx)("input",{type:"text",placeholder:"Buscar en logs...",value:a.search,onChange:e=>i({...a,search:e.target.value}),className:"border border-gray-300 dark:border-gray-600 rounded-md px-3 py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-white text-sm flex-1 min-w-0"})]}),(0,n.jsxs)("div",{className:"flex items-center gap-2",children:[(0,n.jsxs)("label",{className:"flex items-center text-sm text-gray-600 dark:text-gray-300",children:[(0,n.jsx)("input",{type:"checkbox",checked:o,onChange:e=>l(e.target.checked),className:"rounded border-gray-300 dark:border-gray-600 text-blue-600 focus:ring-blue-500 mr-2"}),"Auto-scroll"]}),(0,n.jsx)("button",{onClick:t,className:"bg-red-600 hover:bg-red-700 text-white px-3 py-2 rounded-md text-sm font-medium transition-colors",children:"Limpiar"})]})]}),(0,n.jsx)("div",{className:"bg-gray-50 dark:bg-gray-800 rounded-lg p-4",children:(0,n.jsxs)("div",{className:"grid grid-cols-2 sm:grid-cols-5 gap-4 text-center",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("div",{className:"text-lg font-semibold text-gray-900 dark:text-white",children:e.length}),(0,n.jsx)("div",{className:"text-xs text-gray-500 dark:text-gray-400",children:"Total"})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("div",{className:"text-lg font-semibold text-red-600",children:e.filter(e=>"error"===e.level).length}),(0,n.jsx)("div",{className:"text-xs text-gray-500 dark:text-gray-400",children:"Errores"})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("div",{className:"text-lg font-semibold text-yellow-600",children:e.filter(e=>"warn"===e.level).length}),(0,n.jsx)("div",{className:"text-xs text-gray-500 dark:text-gray-400",children:"Advertencias"})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("div",{className:"text-lg font-semibold text-blue-600",children:e.filter(e=>"info"===e.level).length}),(0,n.jsx)("div",{className:"text-xs text-gray-500 dark:text-gray-400",children:"Info"})]}),(0,n.jsxs)("div",{children:[(0,n.jsx)("div",{className:"text-lg font-semibold text-gray-600",children:e.filter(e=>"debug"===e.level).length}),(0,n.jsx)("div",{className:"text-xs text-gray-500 dark:text-gray-400",children:"Debug"})]})]})}),(0,n.jsxs)("div",{className:"bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg",children:[(0,n.jsx)("div",{className:"max-h-96 overflow-y-auto",children:0===d.length?(0,n.jsx)("div",{className:"text-center py-8",children:(0,n.jsx)("p",{className:"text-gray-500 dark:text-gray-400",children:0===e.length?"No hay logs disponibles":"No hay logs que coincidan con los filtros"})}):(0,n.jsx)("div",{className:"divide-y divide-gray-200 dark:divide-gray-700",children:d.map((e,t)=>(0,n.jsx)("div",{className:"p-4 hover:bg-gray-50 dark:hover:bg-gray-700/50",children:(0,n.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,n.jsx)("div",{className:`flex-shrink-0 p-1 rounded ${u(e.level)}`,children:m(e.level)}),(0,n.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,n.jsxs)("div",{className:"flex items-center justify-between",children:[(0,n.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,n.jsx)("span",{className:`inline-flex items-center px-2 py-0.5 rounded text-xs font-medium ${u(e.level)}`,children:e.level.toUpperCase()}),(0,n.jsx)("span",{className:"text-xs text-gray-500 dark:text-gray-400",children:h(e.connectionId)})]}),(0,n.jsx)("span",{className:"text-xs text-gray-500 dark:text-gray-400",children:e.timestamp.toLocaleTimeString()})]}),(0,n.jsx)("p",{className:"mt-1 text-sm text-gray-900 dark:text-white",children:e.message}),e.data&&(0,n.jsxs)("details",{className:"mt-2",children:[(0,n.jsx)("summary",{className:"text-xs text-gray-500 dark:text-gray-400 cursor-pointer hover:text-gray-700 dark:hover:text-gray-300",children:"Ver datos adicionales"}),(0,n.jsx)("pre",{className:"mt-1 text-xs bg-gray-100 dark:bg-gray-800 p-2 rounded overflow-x-auto",children:"string"==typeof e.data?e.data:JSON.stringify(e.data,null,2)})]})]})]})},t))})}),(0,n.jsx)("div",{ref:c})]})]})}function C(){let[e,t]=(0,s.useState)("connections"),[r,a]=(0,s.useState)(!1),{connections:i,activeConnection:o,getActiveConnection:l}=v(),c=l(),d=i.filter(e=>e.status.connected).length,u=[{id:"connections",label:"Conexiones",count:i.length},{id:"tools",label:"Herramientas",count:c?.tools.length||0},{id:"resources",label:"Recursos",count:c?.resources.length||0},{id:"prompts",label:"Prompts",count:c?.prompts.length||0},{id:"logs",label:"Logs",count:0}];return(0,n.jsxs)("div",{className:"space-y-6",children:[(0,n.jsx)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6",children:(0,n.jsxs)("div",{className:"flex items-center justify-between",children:[(0,n.jsxs)("div",{children:[(0,n.jsx)("h2",{className:"text-lg font-semibold text-gray-900 dark:text-white",children:"Estado del Cliente MCP"}),(0,n.jsxs)("p",{className:"text-sm text-gray-500 dark:text-gray-400 mt-1",children:[d," de ",i.length," conexiones activas",o&&(0,n.jsxs)("span",{className:"ml-2",children:["• Conexi\xf3n activa: ",(0,n.jsx)("span",{className:"font-medium",children:c?.name})]})]})]}),(0,n.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,n.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,n.jsx)("div",{className:`w-3 h-3 rounded-full ${d>0?"bg-green-500":"bg-gray-400"}`}),(0,n.jsx)("span",{className:"text-sm text-gray-600 dark:text-gray-300",children:d>0?"Conectado":"Desconectado"})]}),(0,n.jsx)("button",{onClick:()=>a(!0),className:"bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors",children:"Nueva Conexi\xf3n"})]})]})}),(0,n.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700",children:[(0,n.jsx)("div",{className:"border-b border-gray-200 dark:border-gray-700",children:(0,n.jsx)("nav",{className:"flex space-x-8 px-6","aria-label":"Tabs",children:u.map(r=>(0,n.jsxs)("button",{onClick:()=>t(r.id),className:`py-4 px-1 border-b-2 font-medium text-sm transition-colors ${e===r.id?"border-blue-500 text-blue-600 dark:text-blue-400":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 dark:text-gray-400 dark:hover:text-gray-300"}`,children:[r.label,r.count>0&&(0,n.jsx)("span",{className:`ml-2 py-0.5 px-2 rounded-full text-xs ${e===r.id?"bg-blue-100 text-blue-600 dark:bg-blue-900 dark:text-blue-300":"bg-gray-100 text-gray-600 dark:bg-gray-700 dark:text-gray-300"}`,children:r.count})]},r.id))})}),(0,n.jsxs)("div",{className:"p-6",children:["connections"===e&&(0,n.jsx)(j,{}),"tools"===e&&(0,n.jsx)(N,{}),"resources"===e&&(0,n.jsx)(E,{}),"prompts"===e&&(0,n.jsx)(w,{}),"logs"===e&&(0,n.jsx)(S,{})]})]}),r&&(0,n.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50",children:(0,n.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto",children:[(0,n.jsxs)("div",{className:"flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700",children:[(0,n.jsx)("h3",{className:"text-lg font-semibold text-gray-900 dark:text-white",children:"Nueva Conexi\xf3n MCP"}),(0,n.jsx)("button",{onClick:()=>a(!1),className:"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300",children:(0,n.jsx)("svg",{className:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,n.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})})]}),(0,n.jsx)("div",{className:"p-6",children:(0,n.jsx)(k,{onSuccess:()=>{a(!1),t("connections")},onCancel:()=>a(!1)})})]})})]})}function P(){return(0,n.jsx)(b,{children:(0,n.jsxs)("div",{className:"min-h-screen bg-gray-50 dark:bg-gray-900",children:[(0,n.jsx)("header",{className:"bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700",children:(0,n.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,n.jsxs)("div",{className:"flex justify-between items-center py-4",children:[(0,n.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,n.jsx)("div",{className:"w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center",children:(0,n.jsx)("span",{className:"text-white font-bold text-sm",children:"MCP"})}),(0,n.jsxs)("div",{children:[(0,n.jsx)("h1",{className:"text-xl font-semibold text-gray-900 dark:text-white",children:"MCP Web Client"}),(0,n.jsx)("p",{className:"text-sm text-gray-500 dark:text-gray-400",children:"Model Context Protocol Client"})]})]}),(0,n.jsx)("div",{className:"flex items-center space-x-2",children:(0,n.jsx)("span",{className:"text-sm text-gray-500 dark:text-gray-400",children:"v1.0.0"})})]})})}),(0,n.jsx)("main",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:(0,n.jsx)(C,{})})]})})}},2785:(e,t)=>{"use strict";function r(e){let t={};for(let[r,n]of e.entries()){let e=t[r];void 0===e?t[r]=n:Array.isArray(e)?e.push(n):t[r]=[e,n]}return t}function n(e){return"string"==typeof e?e:("number"!=typeof e||isNaN(e))&&"boolean"!=typeof e?"":String(e)}function s(e){let t=new URLSearchParams;for(let[r,s]of Object.entries(e))if(Array.isArray(s))for(let e of s)t.append(r,n(e));else t.set(r,n(s));return t}function a(e){for(var t=arguments.length,r=Array(t>1?t-1:0),n=1;n<t;n++)r[n-1]=arguments[n];for(let t of r){for(let r of t.keys())e.delete(r);for(let[r,n]of t.entries())e.append(r,n)}return e}Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{assign:function(){return a},searchParamsToUrlQuery:function(){return r},urlQueryToSearchParams:function(){return s}})},2958:(e,t)=>{"use strict";function r(e){return e.replace(/\\/g,"/")}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"normalizePathSep",{enumerable:!0,get:function(){return r}})},3033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},3293:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"escapeStringRegexp",{enumerable:!0,get:function(){return s}});let r=/[|\\{}()[\]^$+*?.-]/,n=/[|\\{}()[\]^$+*?.-]/g;function s(e){return r.test(e)?e.replace(n,"\\$&"):e}},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3495:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,6444,23)),Promise.resolve().then(r.t.bind(r,6042,23)),Promise.resolve().then(r.t.bind(r,8170,23)),Promise.resolve().then(r.t.bind(r,9477,23)),Promise.resolve().then(r.t.bind(r,9345,23)),Promise.resolve().then(r.t.bind(r,2089,23)),Promise.resolve().then(r.t.bind(r,6577,23)),Promise.resolve().then(r.t.bind(r,1307,23))},3736:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"parseRelativeUrl",{enumerable:!0,get:function(){return s}}),r(4827);let n=r(2785);function s(e,t,r){void 0===r&&(r=!0);let s=new URL("http://n"),a=t?new URL(t,s):e.startsWith(".")?new URL("http://n"):s,{pathname:i,searchParams:o,search:l,hash:c,href:d,origin:u}=new URL(e,a);if(u!==s.origin)throw Object.defineProperty(Error("invariant: invalid relative URL, router received "+e),"__NEXT_ERROR_CODE",{value:"E159",enumerable:!1,configurable:!0});return{pathname:i,query:r?(0,n.searchParamsToUrlQuery)(o):void 0,search:l,hash:c,href:d.slice(u.length)}}},3759:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,6346,23)),Promise.resolve().then(r.t.bind(r,7924,23)),Promise.resolve().then(r.t.bind(r,5656,23)),Promise.resolve().then(r.t.bind(r,99,23)),Promise.resolve().then(r.t.bind(r,8243,23)),Promise.resolve().then(r.t.bind(r,8827,23)),Promise.resolve().then(r.t.bind(r,2763,23)),Promise.resolve().then(r.t.bind(r,7173,23))},3873:e=>{"use strict";e.exports=require("path")},4221:(e,t,r)=>{Promise.resolve().then(r.bind(r,1204))},4396:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{getNamedMiddlewareRegex:function(){return g},getNamedRouteRegex:function(){return p},getRouteRegex:function(){return u},parseParameter:function(){return l}});let n=r(6143),s=r(1437),a=r(3293),i=r(2887),o=/^([^[]*)\[((?:\[[^\]]*\])|[^\]]+)\](.*)$/;function l(e){let t=e.match(o);return t?c(t[2]):c(e)}function c(e){let t=e.startsWith("[")&&e.endsWith("]");t&&(e=e.slice(1,-1));let r=e.startsWith("...");return r&&(e=e.slice(3)),{key:e,repeat:r,optional:t}}function d(e,t,r){let n={},l=1,d=[];for(let u of(0,i.removeTrailingSlash)(e).slice(1).split("/")){let e=s.INTERCEPTION_ROUTE_MARKERS.find(e=>u.startsWith(e)),i=u.match(o);if(e&&i&&i[2]){let{key:t,optional:r,repeat:s}=c(i[2]);n[t]={pos:l++,repeat:s,optional:r},d.push("/"+(0,a.escapeStringRegexp)(e)+"([^/]+?)")}else if(i&&i[2]){let{key:e,repeat:t,optional:s}=c(i[2]);n[e]={pos:l++,repeat:t,optional:s},r&&i[1]&&d.push("/"+(0,a.escapeStringRegexp)(i[1]));let o=t?s?"(?:/(.+?))?":"/(.+?)":"/([^/]+?)";r&&i[1]&&(o=o.substring(1)),d.push(o)}else d.push("/"+(0,a.escapeStringRegexp)(u));t&&i&&i[3]&&d.push((0,a.escapeStringRegexp)(i[3]))}return{parameterizedRoute:d.join(""),groups:n}}function u(e,t){let{includeSuffix:r=!1,includePrefix:n=!1,excludeOptionalTrailingSlash:s=!1}=void 0===t?{}:t,{parameterizedRoute:a,groups:i}=d(e,r,n),o=a;return s||(o+="(?:/)?"),{re:RegExp("^"+o+"$"),groups:i}}function m(e){let t,{interceptionMarker:r,getSafeRouteKey:n,segment:s,routeKeys:i,keyPrefix:o,backreferenceDuplicateKeys:l}=e,{key:d,optional:u,repeat:m}=c(s),h=d.replace(/\W/g,"");o&&(h=""+o+h);let p=!1;(0===h.length||h.length>30)&&(p=!0),isNaN(parseInt(h.slice(0,1)))||(p=!0),p&&(h=n());let g=h in i;o?i[h]=""+o+d:i[h]=d;let x=r?(0,a.escapeStringRegexp)(r):"";return t=g&&l?"\\k<"+h+">":m?"(?<"+h+">.+?)":"(?<"+h+">[^/]+?)",u?"(?:/"+x+t+")?":"/"+x+t}function h(e,t,r,l,c){let d,u=(d=0,()=>{let e="",t=++d;for(;t>0;)e+=String.fromCharCode(97+(t-1)%26),t=Math.floor((t-1)/26);return e}),h={},p=[];for(let d of(0,i.removeTrailingSlash)(e).slice(1).split("/")){let e=s.INTERCEPTION_ROUTE_MARKERS.some(e=>d.startsWith(e)),i=d.match(o);if(e&&i&&i[2])p.push(m({getSafeRouteKey:u,interceptionMarker:i[1],segment:i[2],routeKeys:h,keyPrefix:t?n.NEXT_INTERCEPTION_MARKER_PREFIX:void 0,backreferenceDuplicateKeys:c}));else if(i&&i[2]){l&&i[1]&&p.push("/"+(0,a.escapeStringRegexp)(i[1]));let e=m({getSafeRouteKey:u,segment:i[2],routeKeys:h,keyPrefix:t?n.NEXT_QUERY_PARAM_PREFIX:void 0,backreferenceDuplicateKeys:c});l&&i[1]&&(e=e.substring(1)),p.push(e)}else p.push("/"+(0,a.escapeStringRegexp)(d));r&&i&&i[3]&&p.push((0,a.escapeStringRegexp)(i[3]))}return{namedParameterizedRoute:p.join(""),routeKeys:h}}function p(e,t){var r,n,s;let a=h(e,t.prefixRouteKeys,null!=(r=t.includeSuffix)&&r,null!=(n=t.includePrefix)&&n,null!=(s=t.backreferenceDuplicateKeys)&&s),i=a.namedParameterizedRoute;return t.excludeOptionalTrailingSlash||(i+="(?:/)?"),{...u(e,t),namedRegex:"^"+i+"$",routeKeys:a.routeKeys}}function g(e,t){let{parameterizedRoute:r}=d(e,!1,!1),{catchAll:n=!0}=t;if("/"===r)return{namedRegex:"^/"+(n?".*":"")+"$"};let{namedParameterizedRoute:s}=h(e,!1,!1,!1,!1);return{namedRegex:"^"+s+(n?"(?:(/.*)?)":"")+"$"}}},4431:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>c,metadata:()=>l});var n=r(7413),s=r(2376),a=r.n(s),i=r(8726),o=r.n(i);r(1135);let l={title:"Create Next App",description:"Generated by create next app"};function c({children:e}){return(0,n.jsx)("html",{lang:"en",children:(0,n.jsx)("body",{className:`${a().variable} ${o().variable} antialiased`,children:e})})}},4722:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{normalizeAppPath:function(){return a},normalizeRscURL:function(){return i}});let n=r(5531),s=r(5499);function a(e){return(0,n.ensureLeadingSlash)(e.split("/").reduce((e,t,r,n)=>!t||(0,s.isGroupSegment)(t)||"@"===t[0]||("page"===t||"route"===t)&&r===n.length-1?e:e+"/"+t,""))}function i(e){return e.replace(/\.rsc($|\?)/,"$1")}},4827:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{DecodeError:function(){return p},MiddlewareNotFoundError:function(){return y},MissingStaticPage:function(){return f},NormalizeError:function(){return g},PageNotFoundError:function(){return x},SP:function(){return m},ST:function(){return h},WEB_VITALS:function(){return r},execOnce:function(){return n},getDisplayName:function(){return l},getLocationOrigin:function(){return i},getURL:function(){return o},isAbsoluteUrl:function(){return a},isResSent:function(){return c},loadGetInitialProps:function(){return u},normalizeRepeatedSlashes:function(){return d},stringifyError:function(){return b}});let r=["CLS","FCP","FID","INP","LCP","TTFB"];function n(e){let t,r=!1;return function(){for(var n=arguments.length,s=Array(n),a=0;a<n;a++)s[a]=arguments[a];return r||(r=!0,t=e(...s)),t}}let s=/^[a-zA-Z][a-zA-Z\d+\-.]*?:/,a=e=>s.test(e);function i(){let{protocol:e,hostname:t,port:r}=window.location;return e+"//"+t+(r?":"+r:"")}function o(){let{href:e}=window.location,t=i();return e.substring(t.length)}function l(e){return"string"==typeof e?e:e.displayName||e.name||"Unknown"}function c(e){return e.finished||e.headersSent}function d(e){let t=e.split("?");return t[0].replace(/\\/g,"/").replace(/\/\/+/g,"/")+(t[1]?"?"+t.slice(1).join("?"):"")}async function u(e,t){let r=t.res||t.ctx&&t.ctx.res;if(!e.getInitialProps)return t.ctx&&t.Component?{pageProps:await u(t.Component,t.ctx)}:{};let n=await e.getInitialProps(t);if(r&&c(r))return n;if(!n)throw Object.defineProperty(Error('"'+l(e)+'.getInitialProps()" should resolve to an object. But found "'+n+'" instead.'),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});return n}let m="undefined"!=typeof performance,h=m&&["mark","measure","getEntriesByName"].every(e=>"function"==typeof performance[e]);class p extends Error{}class g extends Error{}class x extends Error{constructor(e){super(),this.code="ENOENT",this.name="PageNotFoundError",this.message="Cannot find module for page: "+e}}class f extends Error{constructor(e,t){super(),this.message="Failed to load static file for page: "+e+" "+t}}class y extends Error{constructor(){super(),this.code="ENOENT",this.message="Cannot find the middleware module"}}function b(e){return JSON.stringify({message:e.message,stack:e.stack})}},5362:e=>{(()=>{"use strict";"undefined"!=typeof __nccwpck_require__&&(__nccwpck_require__.ab=__dirname+"/");var t={};(()=>{function e(e,t){void 0===t&&(t={});for(var r=function(e){for(var t=[],r=0;r<e.length;){var n=e[r];if("*"===n||"+"===n||"?"===n){t.push({type:"MODIFIER",index:r,value:e[r++]});continue}if("\\"===n){t.push({type:"ESCAPED_CHAR",index:r++,value:e[r++]});continue}if("{"===n){t.push({type:"OPEN",index:r,value:e[r++]});continue}if("}"===n){t.push({type:"CLOSE",index:r,value:e[r++]});continue}if(":"===n){for(var s="",a=r+1;a<e.length;){var i=e.charCodeAt(a);if(i>=48&&i<=57||i>=65&&i<=90||i>=97&&i<=122||95===i){s+=e[a++];continue}break}if(!s)throw TypeError("Missing parameter name at "+r);t.push({type:"NAME",index:r,value:s}),r=a;continue}if("("===n){var o=1,l="",a=r+1;if("?"===e[a])throw TypeError('Pattern cannot start with "?" at '+a);for(;a<e.length;){if("\\"===e[a]){l+=e[a++]+e[a++];continue}if(")"===e[a]){if(0==--o){a++;break}}else if("("===e[a]&&(o++,"?"!==e[a+1]))throw TypeError("Capturing groups are not allowed at "+a);l+=e[a++]}if(o)throw TypeError("Unbalanced pattern at "+r);if(!l)throw TypeError("Missing pattern at "+r);t.push({type:"PATTERN",index:r,value:l}),r=a;continue}t.push({type:"CHAR",index:r,value:e[r++]})}return t.push({type:"END",index:r,value:""}),t}(e),n=t.prefixes,a=void 0===n?"./":n,i="[^"+s(t.delimiter||"/#?")+"]+?",o=[],l=0,c=0,d="",u=function(e){if(c<r.length&&r[c].type===e)return r[c++].value},m=function(e){var t=u(e);if(void 0!==t)return t;var n=r[c];throw TypeError("Unexpected "+n.type+" at "+n.index+", expected "+e)},h=function(){for(var e,t="";e=u("CHAR")||u("ESCAPED_CHAR");)t+=e;return t};c<r.length;){var p=u("CHAR"),g=u("NAME"),x=u("PATTERN");if(g||x){var f=p||"";-1===a.indexOf(f)&&(d+=f,f=""),d&&(o.push(d),d=""),o.push({name:g||l++,prefix:f,suffix:"",pattern:x||i,modifier:u("MODIFIER")||""});continue}var y=p||u("ESCAPED_CHAR");if(y){d+=y;continue}if(d&&(o.push(d),d=""),u("OPEN")){var f=h(),b=u("NAME")||"",v=u("PATTERN")||"",j=h();m("CLOSE"),o.push({name:b||(v?l++:""),pattern:b&&!v?i:v,prefix:f,suffix:j,modifier:u("MODIFIER")||""});continue}m("END")}return o}function r(e,t){void 0===t&&(t={});var r=a(t),n=t.encode,s=void 0===n?function(e){return e}:n,i=t.validate,o=void 0===i||i,l=e.map(function(e){if("object"==typeof e)return RegExp("^(?:"+e.pattern+")$",r)});return function(t){for(var r="",n=0;n<e.length;n++){var a=e[n];if("string"==typeof a){r+=a;continue}var i=t?t[a.name]:void 0,c="?"===a.modifier||"*"===a.modifier,d="*"===a.modifier||"+"===a.modifier;if(Array.isArray(i)){if(!d)throw TypeError('Expected "'+a.name+'" to not repeat, but got an array');if(0===i.length){if(c)continue;throw TypeError('Expected "'+a.name+'" to not be empty')}for(var u=0;u<i.length;u++){var m=s(i[u],a);if(o&&!l[n].test(m))throw TypeError('Expected all "'+a.name+'" to match "'+a.pattern+'", but got "'+m+'"');r+=a.prefix+m+a.suffix}continue}if("string"==typeof i||"number"==typeof i){var m=s(String(i),a);if(o&&!l[n].test(m))throw TypeError('Expected "'+a.name+'" to match "'+a.pattern+'", but got "'+m+'"');r+=a.prefix+m+a.suffix;continue}if(!c){var h=d?"an array":"a string";throw TypeError('Expected "'+a.name+'" to be '+h)}}return r}}function n(e,t,r){void 0===r&&(r={});var n=r.decode,s=void 0===n?function(e){return e}:n;return function(r){var n=e.exec(r);if(!n)return!1;for(var a=n[0],i=n.index,o=Object.create(null),l=1;l<n.length;l++)!function(e){if(void 0!==n[e]){var r=t[e-1];"*"===r.modifier||"+"===r.modifier?o[r.name]=n[e].split(r.prefix+r.suffix).map(function(e){return s(e,r)}):o[r.name]=s(n[e],r)}}(l);return{path:a,index:i,params:o}}}function s(e){return e.replace(/([.+*?=^!:${}()[\]|/\\])/g,"\\$1")}function a(e){return e&&e.sensitive?"":"i"}function i(e,t,r){void 0===r&&(r={});for(var n=r.strict,i=void 0!==n&&n,o=r.start,l=r.end,c=r.encode,d=void 0===c?function(e){return e}:c,u="["+s(r.endsWith||"")+"]|$",m="["+s(r.delimiter||"/#?")+"]",h=void 0===o||o?"^":"",p=0;p<e.length;p++){var g=e[p];if("string"==typeof g)h+=s(d(g));else{var x=s(d(g.prefix)),f=s(d(g.suffix));if(g.pattern)if(t&&t.push(g),x||f)if("+"===g.modifier||"*"===g.modifier){var y="*"===g.modifier?"?":"";h+="(?:"+x+"((?:"+g.pattern+")(?:"+f+x+"(?:"+g.pattern+"))*)"+f+")"+y}else h+="(?:"+x+"("+g.pattern+")"+f+")"+g.modifier;else h+="("+g.pattern+")"+g.modifier;else h+="(?:"+x+f+")"+g.modifier}}if(void 0===l||l)i||(h+=m+"?"),h+=r.endsWith?"(?="+u+")":"$";else{var b=e[e.length-1],v="string"==typeof b?m.indexOf(b[b.length-1])>-1:void 0===b;i||(h+="(?:"+m+"(?="+u+"))?"),v||(h+="(?="+m+"|"+u+")")}return new RegExp(h,a(r))}function o(t,r,n){if(t instanceof RegExp){if(!r)return t;var s=t.source.match(/\((?!\?)/g);if(s)for(var l=0;l<s.length;l++)r.push({name:l,prefix:"",suffix:"",modifier:"",pattern:""});return t}return Array.isArray(t)?RegExp("(?:"+t.map(function(e){return o(e,r,n).source}).join("|")+")",a(n)):i(e(t,n),r,n)}Object.defineProperty(t,"__esModule",{value:!0}),t.parse=e,t.compile=function(t,n){return r(e(t,n),n)},t.tokensToFunction=r,t.match=function(e,t){var r=[];return n(o(e,r,t),r,t)},t.regexpToFunction=n,t.tokensToRegexp=i,t.pathToRegexp=o})(),e.exports=t})()},5526:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{compileNonPath:function(){return d},matchHas:function(){return c},parseDestination:function(){return u},prepareDestination:function(){return m}});let n=r(5362),s=r(3293),a=r(6759),i=r(1437),o=r(8212);function l(e){return e.replace(/__ESC_COLON_/gi,":")}function c(e,t,r,n){void 0===r&&(r=[]),void 0===n&&(n=[]);let s={},a=r=>{let n,a=r.key;switch(r.type){case"header":a=a.toLowerCase(),n=e.headers[a];break;case"cookie":n="cookies"in e?e.cookies[r.key]:(0,o.getCookieParser)(e.headers)()[r.key];break;case"query":n=t[a];break;case"host":{let{host:t}=(null==e?void 0:e.headers)||{};n=null==t?void 0:t.split(":",1)[0].toLowerCase()}}if(!r.value&&n)return s[function(e){let t="";for(let r=0;r<e.length;r++){let n=e.charCodeAt(r);(n>64&&n<91||n>96&&n<123)&&(t+=e[r])}return t}(a)]=n,!0;if(n){let e=RegExp("^"+r.value+"$"),t=Array.isArray(n)?n.slice(-1)[0].match(e):n.match(e);if(t)return Array.isArray(t)&&(t.groups?Object.keys(t.groups).forEach(e=>{s[e]=t.groups[e]}):"host"===r.type&&t[0]&&(s.host=t[0])),!0}return!1};return!(!r.every(e=>a(e))||n.some(e=>a(e)))&&s}function d(e,t){if(!e.includes(":"))return e;for(let r of Object.keys(t))e.includes(":"+r)&&(e=e.replace(RegExp(":"+r+"\\*","g"),":"+r+"--ESCAPED_PARAM_ASTERISKS").replace(RegExp(":"+r+"\\?","g"),":"+r+"--ESCAPED_PARAM_QUESTION").replace(RegExp(":"+r+"\\+","g"),":"+r+"--ESCAPED_PARAM_PLUS").replace(RegExp(":"+r+"(?!\\w)","g"),"--ESCAPED_PARAM_COLON"+r));return e=e.replace(/(:|\*|\?|\+|\(|\)|\{|\})/g,"\\$1").replace(/--ESCAPED_PARAM_PLUS/g,"+").replace(/--ESCAPED_PARAM_COLON/g,":").replace(/--ESCAPED_PARAM_QUESTION/g,"?").replace(/--ESCAPED_PARAM_ASTERISKS/g,"*"),(0,n.compile)("/"+e,{validate:!1})(t).slice(1)}function u(e){let t=e.destination;for(let r of Object.keys({...e.params,...e.query}))r&&(t=t.replace(RegExp(":"+(0,s.escapeStringRegexp)(r),"g"),"__ESC_COLON_"+r));let r=(0,a.parseUrl)(t),n=r.pathname;n&&(n=l(n));let i=r.href;i&&(i=l(i));let o=r.hostname;o&&(o=l(o));let c=r.hash;return c&&(c=l(c)),{...r,pathname:n,hostname:o,href:i,hash:c}}function m(e){let t,r,s=Object.assign({},e.query),a=u(e),{hostname:o,query:c}=a,m=a.pathname;a.hash&&(m=""+m+a.hash);let h=[],p=[];for(let e of((0,n.pathToRegexp)(m,p),p))h.push(e.name);if(o){let e=[];for(let t of((0,n.pathToRegexp)(o,e),e))h.push(t.name)}let g=(0,n.compile)(m,{validate:!1});for(let[r,s]of(o&&(t=(0,n.compile)(o,{validate:!1})),Object.entries(c)))Array.isArray(s)?c[r]=s.map(t=>d(l(t),e.params)):"string"==typeof s&&(c[r]=d(l(s),e.params));let x=Object.keys(e.params).filter(e=>"nextInternalLocale"!==e);if(e.appendParamsToQuery&&!x.some(e=>h.includes(e)))for(let t of x)t in c||(c[t]=e.params[t]);if((0,i.isInterceptionRouteAppPath)(m))for(let t of m.split("/")){let r=i.INTERCEPTION_ROUTE_MARKERS.find(e=>t.startsWith(e));if(r){"(..)(..)"===r?(e.params["0"]="(..)",e.params["1"]="(..)"):e.params["0"]=r;break}}try{let[n,s]=(r=g(e.params)).split("#",2);t&&(a.hostname=t(e.params)),a.pathname=n,a.hash=(s?"#":"")+(s||""),delete a.search}catch(e){if(e.message.match(/Expected .*? to not repeat, but got an array/))throw Object.defineProperty(Error("To use a multi-match in the destination you must add `*` at the end of the param name to signify it should repeat. https://nextjs.org/docs/messages/invalid-multi-match"),"__NEXT_ERROR_CODE",{value:"E329",enumerable:!1,configurable:!0});throw e}return a.query={...s,...a.query},{newUrl:r,destQuery:c,parsedDestination:a}}},5531:(e,t)=>{"use strict";function r(e){return e.startsWith("/")?e:"/"+e}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"ensureLeadingSlash",{enumerable:!0,get:function(){return r}})},6341:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{getPreviouslyRevalidatedTags:function(){return f},getUtils:function(){return x},interpolateDynamicPath:function(){return p},normalizeDynamicRouteParams:function(){return g},normalizeVercelUrl:function(){return h}});let n=r(9551),s=r(1959),a=r(2437),i=r(4396),o=r(8034),l=r(5526),c=r(2887),d=r(4722),u=r(6143),m=r(7912);function h(e,t,r){let s=(0,n.parse)(e.url,!0);for(let e of(delete s.search,Object.keys(s.query))){let n=e!==u.NEXT_QUERY_PARAM_PREFIX&&e.startsWith(u.NEXT_QUERY_PARAM_PREFIX),a=e!==u.NEXT_INTERCEPTION_MARKER_PREFIX&&e.startsWith(u.NEXT_INTERCEPTION_MARKER_PREFIX);(n||a||t.includes(e)||r&&Object.keys(r.groups).includes(e))&&delete s.query[e]}e.url=(0,n.format)(s)}function p(e,t,r){if(!r)return e;for(let n of Object.keys(r.groups)){let s,{optional:a,repeat:i}=r.groups[n],o=`[${i?"...":""}${n}]`;a&&(o=`[${o}]`);let l=t[n];s=Array.isArray(l)?l.map(e=>e&&encodeURIComponent(e)).join("/"):l?encodeURIComponent(l):"",e=e.replaceAll(o,s)}return e}function g(e,t,r,n){let s={};for(let a of Object.keys(t.groups)){let i=e[a];"string"==typeof i?i=(0,d.normalizeRscURL)(i):Array.isArray(i)&&(i=i.map(d.normalizeRscURL));let o=r[a],l=t.groups[a].optional;if((Array.isArray(o)?o.some(e=>Array.isArray(i)?i.some(t=>t.includes(e)):null==i?void 0:i.includes(e)):null==i?void 0:i.includes(o))||void 0===i&&!(l&&n))return{params:{},hasValidParams:!1};l&&(!i||Array.isArray(i)&&1===i.length&&("index"===i[0]||i[0]===`[[...${a}]]`))&&(i=void 0,delete e[a]),i&&"string"==typeof i&&t.groups[a].repeat&&(i=i.split("/")),i&&(s[a]=i)}return{params:s,hasValidParams:!0}}function x({page:e,i18n:t,basePath:r,rewrites:n,pageIsDynamic:d,trailingSlash:u,caseSensitive:x}){let f,y,b;return d&&(f=(0,i.getNamedRouteRegex)(e,{prefixRouteKeys:!1}),b=(y=(0,o.getRouteMatcher)(f))(e)),{handleRewrites:function(i,o){let m={},h=o.pathname,p=n=>{let c=(0,a.getPathMatch)(n.source+(u?"(/)?":""),{removeUnnamedParams:!0,strict:!0,sensitive:!!x});if(!o.pathname)return!1;let p=c(o.pathname);if((n.has||n.missing)&&p){let e=(0,l.matchHas)(i,o.query,n.has,n.missing);e?Object.assign(p,e):p=!1}if(p){let{parsedDestination:a,destQuery:i}=(0,l.prepareDestination)({appendParamsToQuery:!0,destination:n.destination,params:p,query:o.query});if(a.protocol)return!0;if(Object.assign(m,i,p),Object.assign(o.query,a.query),delete a.query,Object.assign(o,a),!(h=o.pathname))return!1;if(r&&(h=h.replace(RegExp(`^${r}`),"")||"/"),t){let e=(0,s.normalizeLocalePath)(h,t.locales);h=e.pathname,o.query.nextInternalLocale=e.detectedLocale||p.nextInternalLocale}if(h===e)return!0;if(d&&y){let e=y(h);if(e)return o.query={...o.query,...e},!0}}return!1};for(let e of n.beforeFiles||[])p(e);if(h!==e){let t=!1;for(let e of n.afterFiles||[])if(t=p(e))break;if(!t&&!(()=>{let t=(0,c.removeTrailingSlash)(h||"");return t===(0,c.removeTrailingSlash)(e)||(null==y?void 0:y(t))})()){for(let e of n.fallback||[])if(t=p(e))break}}return m},defaultRouteRegex:f,dynamicRouteMatcher:y,defaultRouteMatches:b,getParamsFromRouteMatches:function(e){if(!f)return null;let{groups:t,routeKeys:r}=f,n=(0,o.getRouteMatcher)({re:{exec:e=>{let n=Object.fromEntries(new URLSearchParams(e));for(let[e,t]of Object.entries(n)){let r=(0,m.normalizeNextQueryParam)(e);r&&(n[r]=t,delete n[e])}let s={};for(let e of Object.keys(r)){let a=r[e];if(!a)continue;let i=t[a],o=n[e];if(!i.optional&&!o)return null;s[i.pos]=o}return s}},groups:t})(e);return n||null},normalizeDynamicRouteParams:(e,t)=>f&&b?g(e,f,b,t):{params:{},hasValidParams:!1},normalizeVercelUrl:(e,t)=>h(e,t,f),interpolateDynamicPath:(e,t)=>p(e,t,f)}}function f(e,t){return"string"==typeof e[u.NEXT_CACHE_REVALIDATED_TAGS_HEADER]&&e[u.NEXT_CACHE_REVALIDATE_TAG_TOKEN_HEADER]===t?e[u.NEXT_CACHE_REVALIDATED_TAGS_HEADER].split(","):[]}},6415:e=>{(()=>{"use strict";"undefined"!=typeof __nccwpck_require__&&(__nccwpck_require__.ab=__dirname+"/");var t={};(()=>{t.parse=function(t,r){if("string"!=typeof t)throw TypeError("argument str must be a string");for(var s={},a=t.split(n),i=(r||{}).decode||e,o=0;o<a.length;o++){var l=a[o],c=l.indexOf("=");if(!(c<0)){var d=l.substr(0,c).trim(),u=l.substr(++c,l.length).trim();'"'==u[0]&&(u=u.slice(1,-1)),void 0==s[d]&&(s[d]=function(e,t){try{return t(e)}catch(t){return e}}(u,i))}}return s},t.serialize=function(e,t,n){var a=n||{},i=a.encode||r;if("function"!=typeof i)throw TypeError("option encode is invalid");if(!s.test(e))throw TypeError("argument name is invalid");var o=i(t);if(o&&!s.test(o))throw TypeError("argument val is invalid");var l=e+"="+o;if(null!=a.maxAge){var c=a.maxAge-0;if(isNaN(c)||!isFinite(c))throw TypeError("option maxAge is invalid");l+="; Max-Age="+Math.floor(c)}if(a.domain){if(!s.test(a.domain))throw TypeError("option domain is invalid");l+="; Domain="+a.domain}if(a.path){if(!s.test(a.path))throw TypeError("option path is invalid");l+="; Path="+a.path}if(a.expires){if("function"!=typeof a.expires.toUTCString)throw TypeError("option expires is invalid");l+="; Expires="+a.expires.toUTCString()}if(a.httpOnly&&(l+="; HttpOnly"),a.secure&&(l+="; Secure"),a.sameSite)switch("string"==typeof a.sameSite?a.sameSite.toLowerCase():a.sameSite){case!0:case"strict":l+="; SameSite=Strict";break;case"lax":l+="; SameSite=Lax";break;case"none":l+="; SameSite=None";break;default:throw TypeError("option sameSite is invalid")}return l};var e=decodeURIComponent,r=encodeURIComponent,n=/; */,s=/^[\u0009\u0020-\u007e\u0080-\u00ff]+$/})(),e.exports=t})()},6759:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"parseUrl",{enumerable:!0,get:function(){return a}});let n=r(2785),s=r(3736);function a(e){if(e.startsWith("/"))return(0,s.parseRelativeUrl)(e);let t=new URL(e);return{hash:t.hash,hostname:t.hostname,href:t.href,pathname:t.pathname,port:t.port,protocol:t.protocol,query:(0,n.searchParamsToUrlQuery)(t.searchParams),search:t.search}}},8034:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getRouteMatcher",{enumerable:!0,get:function(){return s}});let n=r(4827);function s(e){let{re:t,groups:r}=e;return e=>{let s=t.exec(e);if(!s)return!1;let a=e=>{try{return decodeURIComponent(e)}catch(e){throw Object.defineProperty(new n.DecodeError("failed to decode param"),"__NEXT_ERROR_CODE",{value:"E528",enumerable:!1,configurable:!0})}},i={};for(let[e,t]of Object.entries(r)){let r=s[t.pos];void 0!==r&&(t.repeat?i[e]=r.split("/").map(e=>a(e)):i[e]=a(r))}return i}}},8212:(e,t,r)=>{"use strict";function n(e){return function(){let{cookie:t}=e;if(!t)return{};let{parse:n}=r(6415);return n(Array.isArray(t)?t.join("; "):t)}}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getCookieParser",{enumerable:!0,get:function(){return n}})},8304:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{DEFAULT_METADATA_ROUTE_EXTENSIONS:function(){return o},STATIC_METADATA_IMAGES:function(){return i},getExtensionRegexString:function(){return l},isMetadataPage:function(){return u},isMetadataRoute:function(){return m},isMetadataRouteFile:function(){return c},isStaticMetadataRoute:function(){return d}});let n=r(2958),s=r(4722),a=r(554),i={icon:{filename:"icon",extensions:["ico","jpg","jpeg","png","svg"]},apple:{filename:"apple-icon",extensions:["jpg","jpeg","png"]},favicon:{filename:"favicon",extensions:["ico"]},openGraph:{filename:"opengraph-image",extensions:["jpg","jpeg","png","gif"]},twitter:{filename:"twitter-image",extensions:["jpg","jpeg","png","gif"]}},o=["js","jsx","ts","tsx"],l=(e,t)=>t&&0!==t.length?`(?:\\.(${e.join("|")})|(\\.(${t.join("|")})))`:`(\\.(?:${e.join("|")}))`;function c(e,t,r){let s=(r?"":"?")+"$",a=`\\d?${r?"":"(-\\w{6})?"}`,o=[RegExp(`^[\\\\/]robots${l(t.concat("txt"),null)}${s}`),RegExp(`^[\\\\/]manifest${l(t.concat("webmanifest","json"),null)}${s}`),RegExp("^[\\\\/]favicon\\.ico$"),RegExp(`[\\\\/]sitemap${l(["xml"],t)}${s}`),RegExp(`[\\\\/]${i.icon.filename}${a}${l(i.icon.extensions,t)}${s}`),RegExp(`[\\\\/]${i.apple.filename}${a}${l(i.apple.extensions,t)}${s}`),RegExp(`[\\\\/]${i.openGraph.filename}${a}${l(i.openGraph.extensions,t)}${s}`),RegExp(`[\\\\/]${i.twitter.filename}${a}${l(i.twitter.extensions,t)}${s}`)],c=(0,n.normalizePathSep)(e);return o.some(e=>e.test(c))}function d(e){let t=e.replace(/\/route$/,"");return(0,a.isAppRouteRoute)(e)&&c(t,[],!0)&&"/robots.txt"!==t&&"/manifest.webmanifest"!==t&&!t.endsWith("/sitemap.xml")}function u(e){return!(0,a.isAppRouteRoute)(e)&&c(e,[],!1)}function m(e){let t=(0,s.normalizeAppPath)(e).replace(/^\/?app\//,"").replace("/[__metadata_id__]","").replace(/\/route$/,"");return"/"!==t[0]&&(t="/"+t),(0,a.isAppRouteRoute)(e)&&c(t,[],!1)}},9121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},9294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},9551:e=>{"use strict";e.exports=require("url")},9646:e=>{"use strict";e.exports=require("child_process")},9810:()=>{}};var t=require("../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),n=t.X(0,[447,145],()=>r(786));module.exports=n})();