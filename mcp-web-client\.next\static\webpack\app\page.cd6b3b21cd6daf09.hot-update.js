"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/lib/mcp/transports/sse-transport.ts":
/*!*************************************************!*\
  !*** ./src/lib/mcp/transports/sse-transport.ts ***!
  \*************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SSETransport: () => (/* binding */ SSETransport),\n/* harmony export */   SSE_PRESETS: () => (/* binding */ SSE_PRESETS),\n/* harmony export */   createSSETransport: () => (/* binding */ createSSETransport),\n/* harmony export */   detectSSESupport: () => (/* binding */ detectSSESupport)\n/* harmony export */ });\n/* harmony import */ var _base_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../base-client */ \"(app-pages-browser)/./src/lib/mcp/base-client.ts\");\n\nclass SSETransport extends _base_client__WEBPACK_IMPORTED_MODULE_0__.BaseMCPTransport {\n    async connect() {\n        if (this.connecting || this.connected) {\n            return;\n        }\n        this.connecting = true;\n        try {\n            await this.establishSSEConnection();\n        } catch (error) {\n            this.connecting = false;\n            throw error;\n        }\n    }\n    async establishSSEConnection() {\n        return new Promise((resolve, reject)=>{\n            const url = new URL(this.config.url);\n            // Agregar Last-Event-ID si tenemos uno (para reconexión)\n            if (this.lastEventId) {\n                url.searchParams.set('Last-Event-ID', this.lastEventId);\n            }\n            try {\n                this.eventSource = new EventSource(url.toString());\n            } catch (error) {\n                reject(new Error(\"Failed to create EventSource: \".concat(error)));\n                return;\n            }\n            const timeout = setTimeout(()=>{\n                this.cleanup();\n                reject(new Error('Timeout connecting to SSE endpoint'));\n            }, 10000);\n            let connectionEstablished = false;\n            this.eventSource.onopen = ()=>{\n                console.log('SSE connection opened');\n                // Si no recibimos un evento 'endpoint' en 2 segundos, asumir que la conexión está lista\n                setTimeout(()=>{\n                    if (!connectionEstablished) {\n                        clearTimeout(timeout);\n                        connectionEstablished = true;\n                        this.handleConnect();\n                        resolve();\n                    }\n                }, 2000);\n            };\n            this.eventSource.onmessage = (event)=>{\n                try {\n                    const data = JSON.parse(event.data);\n                    // Guardar el ID del evento para reconexión\n                    if (event.lastEventId) {\n                        this.lastEventId = event.lastEventId;\n                    }\n                    // Si recibimos cualquier mensaje válido, considerar la conexión establecida\n                    if (!connectionEstablished) {\n                        clearTimeout(timeout);\n                        connectionEstablished = true;\n                        this.handleConnect();\n                        resolve();\n                    }\n                    this.handleMessage(data);\n                } catch (error) {\n                    console.error('Error parsing SSE message:', error);\n                }\n            };\n            // Listener opcional para el evento 'endpoint' (algunos servidores lo envían)\n            this.eventSource.addEventListener('endpoint', (event)=>{\n                try {\n                    const data = JSON.parse(event.data);\n                    if (data.uri) {\n                        this.messageEndpoint = data.uri;\n                    }\n                    if (!connectionEstablished) {\n                        clearTimeout(timeout);\n                        connectionEstablished = true;\n                        this.handleConnect();\n                        resolve();\n                    }\n                } catch (error) {\n                    console.warn('Error parsing endpoint event:', error);\n                // No rechazar aquí, ya que es opcional\n                }\n            });\n            this.eventSource.addEventListener('message', (event)=>{\n                try {\n                    const message = JSON.parse(event.data);\n                    // Guardar el ID del evento para reconexión\n                    if (event.lastEventId) {\n                        this.lastEventId = event.lastEventId;\n                    }\n                    this.handleMessage(message);\n                } catch (error) {\n                    console.error('Error parsing MCP message from SSE:', error);\n                }\n            });\n            this.eventSource.onerror = ()=>{\n                var _this_eventSource;\n                clearTimeout(timeout);\n                if (((_this_eventSource = this.eventSource) === null || _this_eventSource === void 0 ? void 0 : _this_eventSource.readyState) === EventSource.CLOSED) {\n                    this.handleDisconnect();\n                    reject(new Error('SSE connection closed'));\n                } else {\n                    // Error temporal, intentar reconectar\n                    reject(new Error('SSE connection error'));\n                }\n            };\n        });\n    }\n    handleReconnect() {\n        if (!this.connected) return;\n        console.log('SSE connection lost, attempting to reconnect...');\n        setTimeout(async ()=>{\n            try {\n                await this.establishSSEConnection();\n                console.log('SSE reconnection successful');\n            } catch (error) {\n                console.error('SSE reconnection failed:', error);\n                this.handleError(new Error('Failed to reconnect to SSE endpoint'));\n            }\n        }, 1000);\n    }\n    async send(message) {\n        if (!this.connected) {\n            throw new Error('Not connected to SSE endpoint');\n        }\n        try {\n            const response = await fetch(this.messageEndpoint, {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json',\n                    ...this.sessionId && {\n                        'Mcp-Session-Id': this.sessionId\n                    }\n                },\n                body: JSON.stringify(message)\n            });\n            if (!response.ok) {\n                throw new Error(\"HTTP \".concat(response.status, \": \").concat(response.statusText));\n            }\n            // Verificar si el servidor devolvió un session ID\n            const sessionId = response.headers.get('Mcp-Session-Id');\n            if (sessionId && !this.sessionId) {\n                this.sessionId = sessionId;\n            }\n            // Si la respuesta tiene contenido, procesarlo\n            const contentType = response.headers.get('Content-Type');\n            if (contentType === null || contentType === void 0 ? void 0 : contentType.includes('application/json')) {\n                const responseData = await response.json();\n                if (responseData) {\n                    this.handleMessage(responseData);\n                }\n            }\n        } catch (error) {\n            throw new Error(\"Failed to send message: \".concat(error));\n        }\n    }\n    async disconnect() {\n        this.cleanup();\n        this.handleDisconnect();\n    }\n    cleanup() {\n        if (this.eventSource) {\n            this.eventSource.close();\n            this.eventSource = null;\n        }\n        this.sessionId = null;\n        this.lastEventId = null;\n    }\n    constructor(config){\n        super(), this.eventSource = null, this.sessionId = null, this.lastEventId = null;\n        this.config = config;\n        this.messageEndpoint = config.messageEndpoint || this.config.url.replace('/sse', '/messages');\n    }\n}\n// Función helper para crear transporte SSE\nfunction createSSETransport(config) {\n    return new SSETransport({\n        ...config,\n        type: 'sse'\n    });\n}\n// Función para detectar si un servidor soporta SSE\nasync function detectSSESupport(url) {\n    try {\n        var _response_headers_get;\n        const response = await fetch(url, {\n            method: 'GET',\n            headers: {\n                'Accept': 'text/event-stream'\n            }\n        });\n        return ((_response_headers_get = response.headers.get('Content-Type')) === null || _response_headers_get === void 0 ? void 0 : _response_headers_get.includes('text/event-stream')) || false;\n    } catch (error) {\n        return false;\n    }\n}\n// Configuraciones predefinidas para servidores SSE\nconst SSE_PRESETS = {\n    local: function() {\n        let port = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 3000, path = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : '/sse';\n        return createSSETransport({\n            url: \"http://localhost:\".concat(port).concat(path)\n        });\n    },\n    remote: function(host) {\n        let port = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 3000, path = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : '/sse', secure = arguments.length > 3 && arguments[3] !== void 0 ? arguments[3] : false;\n        return createSSETransport({\n            url: \"\".concat(secure ? 'https' : 'http', \"://\").concat(host, \":\").concat(port).concat(path)\n        });\n    },\n    custom: (url, messageEndpoint)=>createSSETransport({\n            url,\n            messageEndpoint\n        })\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/mcp/transports/sse-transport.ts\n"));

/***/ })

});