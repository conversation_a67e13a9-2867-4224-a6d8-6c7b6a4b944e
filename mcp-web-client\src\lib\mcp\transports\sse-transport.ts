import { BaseMCPTransport } from '../base-client';
import { MCPMessage, TransportConfig } from '@/types/mcp';

export interface SSETransportConfig extends TransportConfig {
  type: 'sse';
  url: string;
  messageEndpoint?: string;
}

export class SSETransport extends BaseMCPTransport {
  private config: SSETransportConfig;
  private eventSource: EventSource | null = null;
  private messageEndpoint: string;
  private sessionId: string | null = null;
  private lastEventId: string | null = null;

  constructor(config: SSETransportConfig) {
    super();
    this.config = config;
    this.messageEndpoint = config.messageEndpoint || this.config.url.replace('/sse', '/messages');
  }

  async connect(): Promise<void> {
    if (this.connecting || this.connected) {
      return;
    }

    this.connecting = true;

    try {
      await this.establishSSEConnection();
    } catch (error) {
      this.connecting = false;
      throw error;
    }
  }

  private async establishSSEConnection(): Promise<void> {
    return new Promise<void>((resolve, reject) => {
      const url = new URL(this.config.url);

      // Agregar Last-Event-ID si tenemos uno (para reconexión)
      if (this.lastEventId) {
        url.searchParams.set('Last-Event-ID', this.lastEventId);
      }

      try {
        this.eventSource = new EventSource(url.toString());
      } catch (error) {
        reject(new Error(`Failed to create EventSource: ${error}`));
        return;
      }

      const timeout = setTimeout(() => {
        this.cleanup();
        reject(new Error('Timeout connecting to SSE endpoint'));
      }, 10000);

      let connectionEstablished = false;

      this.eventSource.onopen = () => {
        console.log('SSE connection opened');

        // Si no recibimos un evento 'endpoint' en 2 segundos, asumir que la conexión está lista
        setTimeout(() => {
          if (!connectionEstablished) {
            clearTimeout(timeout);
            connectionEstablished = true;
            this.handleConnect();
            resolve();
          }
        }, 2000);
      };

      this.eventSource.onmessage = (event) => {
        try {
          const data = JSON.parse(event.data);

          // Guardar el ID del evento para reconexión
          if (event.lastEventId) {
            this.lastEventId = event.lastEventId;
          }

          // Si recibimos cualquier mensaje válido, considerar la conexión establecida
          if (!connectionEstablished) {
            clearTimeout(timeout);
            connectionEstablished = true;
            this.handleConnect();
            resolve();
          }

          this.handleMessage(data);
        } catch (error) {
          console.error('Error parsing SSE message:', error);
        }
      };

      // Listener opcional para el evento 'endpoint' (algunos servidores lo envían)
      this.eventSource.addEventListener('endpoint', (event: any) => {
        try {
          const data = JSON.parse(event.data);
          if (data.uri) {
            this.messageEndpoint = data.uri;
          }

          if (!connectionEstablished) {
            clearTimeout(timeout);
            connectionEstablished = true;
            this.handleConnect();
            resolve();
          }
        } catch (error) {
          console.warn('Error parsing endpoint event:', error);
          // No rechazar aquí, ya que es opcional
        }
      });

      this.eventSource.addEventListener('message', (event: any) => {
        try {
          const message: MCPMessage = JSON.parse(event.data);
          
          // Guardar el ID del evento para reconexión
          if (event.lastEventId) {
            this.lastEventId = event.lastEventId;
          }
          
          this.handleMessage(message);
        } catch (error) {
          console.error('Error parsing MCP message from SSE:', error);
        }
      });

      this.eventSource.onerror = () => {
        if (!connectionEstablished) {
          clearTimeout(timeout);
          reject(new Error('Failed to establish SSE connection. Check if the server supports SSE and CORS is configured.'));
          return;
        }

        if (this.eventSource?.readyState === EventSource.CLOSED) {
          this.handleDisconnect();
        } else {
          // Error temporal, intentar reconectar
          this.handleReconnect();
        }
      };
    });
  }

  private handleReconnect(): void {
    if (!this.connected) return;

    console.log('SSE connection lost, attempting to reconnect...');
    
    setTimeout(async () => {
      try {
        await this.establishSSEConnection();
        console.log('SSE reconnection successful');
      } catch (error) {
        console.error('SSE reconnection failed:', error);
        this.handleError(new Error('Failed to reconnect to SSE endpoint'));
      }
    }, 1000);
  }

  async send(message: MCPMessage): Promise<void> {
    if (!this.connected) {
      throw new Error('Not connected to SSE endpoint');
    }

    try {
      const response = await fetch(this.messageEndpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          ...(this.sessionId && { 'Mcp-Session-Id': this.sessionId })
        },
        body: JSON.stringify(message)
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      // Verificar si el servidor devolvió un session ID
      const sessionId = response.headers.get('Mcp-Session-Id');
      if (sessionId && !this.sessionId) {
        this.sessionId = sessionId;
      }

      // Si la respuesta tiene contenido, procesarlo
      const contentType = response.headers.get('Content-Type');
      if (contentType?.includes('application/json')) {
        const responseData = await response.json();
        if (responseData) {
          this.handleMessage(responseData);
        }
      }

    } catch (error) {
      throw new Error(`Failed to send message: ${error}`);
    }
  }

  async disconnect(): Promise<void> {
    this.cleanup();
    this.handleDisconnect();
  }

  private cleanup(): void {
    if (this.eventSource) {
      this.eventSource.close();
      this.eventSource = null;
    }
    this.sessionId = null;
    this.lastEventId = null;
  }
}

// Función helper para crear transporte SSE
export function createSSETransport(config: Omit<SSETransportConfig, 'type'>): SSETransport {
  return new SSETransport({
    ...config,
    type: 'sse'
  });
}

// Función para detectar si un servidor soporta SSE
export async function detectSSESupport(url: string): Promise<boolean> {
  try {
    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'Accept': 'text/event-stream'
      }
    });

    return response.headers.get('Content-Type')?.includes('text/event-stream') || false;
  } catch (error) {
    return false;
  }
}

// Configuraciones predefinidas para servidores SSE
export const SSE_PRESETS = {
  local: (port: number = 3000, path: string = '/sse') => createSSETransport({
    url: `http://localhost:${port}${path}`
  }),
  
  remote: (host: string, port: number = 3000, path: string = '/sse', secure: boolean = false) => 
    createSSETransport({
      url: `${secure ? 'https' : 'http'}://${host}:${port}${path}`
    }),
  
  custom: (url: string, messageEndpoint?: string) => createSSETransport({
    url,
    messageEndpoint
  })
};
