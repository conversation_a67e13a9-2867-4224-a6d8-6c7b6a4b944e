"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/lib/mcp/transports/sse-transport.ts":
/*!*************************************************!*\
  !*** ./src/lib/mcp/transports/sse-transport.ts ***!
  \*************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SSETransport: () => (/* binding */ SSETransport),\n/* harmony export */   SSE_PRESETS: () => (/* binding */ SSE_PRESETS),\n/* harmony export */   createSSETransport: () => (/* binding */ createSSETransport),\n/* harmony export */   detectSSESupport: () => (/* binding */ detectSSESupport)\n/* harmony export */ });\n/* harmony import */ var _base_client__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ../base-client */ \"(app-pages-browser)/./src/lib/mcp/base-client.ts\");\n\nclass SSETransport extends _base_client__WEBPACK_IMPORTED_MODULE_0__.BaseMCPTransport {\n    async connect() {\n        if (this.connecting || this.connected) {\n            return;\n        }\n        this.connecting = true;\n        try {\n            await this.establishSSEConnection();\n        } catch (error) {\n            this.connecting = false;\n            throw error;\n        }\n    }\n    async establishSSEConnection() {\n        return new Promise((resolve, reject)=>{\n            const url = new URL(this.config.url);\n            // Agregar Last-Event-ID si tenemos uno (para reconexión)\n            if (this.lastEventId) {\n                url.searchParams.set('Last-Event-ID', this.lastEventId);\n            }\n            try {\n                this.eventSource = new EventSource(url.toString());\n            } catch (error) {\n                reject(new Error(\"Failed to create EventSource: \".concat(error)));\n                return;\n            }\n            const timeout = setTimeout(()=>{\n                this.cleanup();\n                reject(new Error('Timeout connecting to SSE endpoint'));\n            }, 10000);\n            let connectionEstablished = false;\n            this.eventSource.onopen = ()=>{\n                console.log('SSE connection opened');\n                // Si no recibimos un evento 'endpoint' en 2 segundos, asumir que la conexión está lista\n                setTimeout(()=>{\n                    if (!connectionEstablished) {\n                        clearTimeout(timeout);\n                        connectionEstablished = true;\n                        this.handleConnect();\n                        resolve();\n                    }\n                }, 2000);\n            };\n            this.eventSource.onmessage = (event)=>{\n                try {\n                    const data = JSON.parse(event.data);\n                    // Guardar el ID del evento para reconexión\n                    if (event.lastEventId) {\n                        this.lastEventId = event.lastEventId;\n                    }\n                    // Si recibimos cualquier mensaje válido, considerar la conexión establecida\n                    if (!connectionEstablished) {\n                        clearTimeout(timeout);\n                        connectionEstablished = true;\n                        this.handleConnect();\n                        resolve();\n                    }\n                    this.handleMessage(data);\n                } catch (error) {\n                    console.error('Error parsing SSE message:', error);\n                }\n            };\n            // Listener opcional para el evento 'endpoint' (algunos servidores lo envían)\n            this.eventSource.addEventListener('endpoint', (event)=>{\n                try {\n                    const data = JSON.parse(event.data);\n                    if (data.uri) {\n                        this.messageEndpoint = data.uri;\n                    }\n                    if (!connectionEstablished) {\n                        clearTimeout(timeout);\n                        connectionEstablished = true;\n                        this.handleConnect();\n                        resolve();\n                    }\n                } catch (error) {\n                    console.warn('Error parsing endpoint event:', error);\n                // No rechazar aquí, ya que es opcional\n                }\n            });\n            this.eventSource.addEventListener('message', (event)=>{\n                try {\n                    const message = JSON.parse(event.data);\n                    // Guardar el ID del evento para reconexión\n                    if (event.lastEventId) {\n                        this.lastEventId = event.lastEventId;\n                    }\n                    this.handleMessage(message);\n                } catch (error) {\n                    console.error('Error parsing MCP message from SSE:', error);\n                }\n            });\n            this.eventSource.onerror = ()=>{\n                var _this_eventSource;\n                if (!connectionEstablished) {\n                    clearTimeout(timeout);\n                    reject(new Error('Failed to establish SSE connection. Check if the server supports SSE and CORS is configured.'));\n                    return;\n                }\n                if (((_this_eventSource = this.eventSource) === null || _this_eventSource === void 0 ? void 0 : _this_eventSource.readyState) === EventSource.CLOSED) {\n                    this.handleDisconnect();\n                } else {\n                    // Error temporal, intentar reconectar\n                    this.handleReconnect();\n                }\n            };\n        });\n    }\n    handleReconnect() {\n        if (!this.connected) return;\n        console.log('SSE connection lost, attempting to reconnect...');\n        setTimeout(async ()=>{\n            try {\n                await this.establishSSEConnection();\n                console.log('SSE reconnection successful');\n            } catch (error) {\n                console.error('SSE reconnection failed:', error);\n                this.handleError(new Error('Failed to reconnect to SSE endpoint'));\n            }\n        }, 1000);\n    }\n    async send(message) {\n        if (!this.connected) {\n            throw new Error('Not connected to SSE endpoint');\n        }\n        try {\n            const response = await fetch(this.messageEndpoint, {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json',\n                    ...this.sessionId && {\n                        'Mcp-Session-Id': this.sessionId\n                    }\n                },\n                body: JSON.stringify(message)\n            });\n            if (!response.ok) {\n                throw new Error(\"HTTP \".concat(response.status, \": \").concat(response.statusText));\n            }\n            // Verificar si el servidor devolvió un session ID\n            const sessionId = response.headers.get('Mcp-Session-Id');\n            if (sessionId && !this.sessionId) {\n                this.sessionId = sessionId;\n            }\n            // Si la respuesta tiene contenido, procesarlo\n            const contentType = response.headers.get('Content-Type');\n            if (contentType === null || contentType === void 0 ? void 0 : contentType.includes('application/json')) {\n                const responseData = await response.json();\n                if (responseData) {\n                    this.handleMessage(responseData);\n                }\n            }\n        } catch (error) {\n            throw new Error(\"Failed to send message: \".concat(error));\n        }\n    }\n    async disconnect() {\n        this.cleanup();\n        this.handleDisconnect();\n    }\n    cleanup() {\n        if (this.eventSource) {\n            this.eventSource.close();\n            this.eventSource = null;\n        }\n        this.sessionId = null;\n        this.lastEventId = null;\n    }\n    constructor(config){\n        super(), this.eventSource = null, this.sessionId = null, this.lastEventId = null;\n        this.config = config;\n        this.messageEndpoint = config.messageEndpoint || this.config.url.replace('/sse', '/messages');\n    }\n}\n// Función helper para crear transporte SSE\nfunction createSSETransport(config) {\n    return new SSETransport({\n        ...config,\n        type: 'sse'\n    });\n}\n// Función para detectar si un servidor soporta SSE\nasync function detectSSESupport(url) {\n    try {\n        var _response_headers_get;\n        const response = await fetch(url, {\n            method: 'GET',\n            headers: {\n                'Accept': 'text/event-stream'\n            }\n        });\n        return ((_response_headers_get = response.headers.get('Content-Type')) === null || _response_headers_get === void 0 ? void 0 : _response_headers_get.includes('text/event-stream')) || false;\n    } catch (error) {\n        return false;\n    }\n}\n// Configuraciones predefinidas para servidores SSE\nconst SSE_PRESETS = {\n    local: function() {\n        let port = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 3000, path = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : '/sse';\n        return createSSETransport({\n            url: \"http://localhost:\".concat(port).concat(path)\n        });\n    },\n    remote: function(host) {\n        let port = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 3000, path = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : '/sse', secure = arguments.length > 3 && arguments[3] !== void 0 ? arguments[3] : false;\n        return createSSETransport({\n            url: \"\".concat(secure ? 'https' : 'http', \"://\").concat(host, \":\").concat(port).concat(path)\n        });\n    },\n    custom: (url, messageEndpoint)=>createSSETransport({\n            url,\n            messageEndpoint\n        })\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/mcp/transports/sse-transport.ts\n"));

/***/ })

});